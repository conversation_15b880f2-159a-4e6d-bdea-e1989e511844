# ASS 字幕系统升级说明

## 概述

后端字幕处理系统已从 SRT 格式升级为 ASS（Advanced SubStation Alpha）格式，提供更丰富的字幕样式和更好的渲染效果。

## 主要改进

### 1. 格式升级

- **从 SRT 到 ASS**: 支持更复杂的字幕样式和效果
- **更好的兼容性**: ASS 格式在 FFmpeg 中有更好的支持
- **丰富的样式选项**: 支持字体、颜色、描边、阴影、渐变等

### 2. 新增功能

#### 字幕样式支持

- ✅ 自定义字体和字号
- ✅ 文字颜色和描边颜色
- ✅ 阴影效果（偏移、模糊）
- ✅ 背景色设置
- ✅ 渐变色效果
- ✅ 文字对齐方式
- ✅ 字符间距调整
- ✅ 响应式字号（基于画布尺寸）

#### 高级特性

- ✅ 多行字幕支持
- ✅ 特殊字符转义
- ✅ 时间格式转换
- ✅ 样式继承和覆盖
- ✅ 文件验证和错误处理

## 技术架构

### 核心文件结构

```
src/ffmpeg/
├── config.ts                    # ASS配置常量
├── utils.ts                     # 主要工具函数
├── utils/
│   └── assSubtitleUtils.ts      # ASS字幕专用工具类
├── FFmpegCommandGenerator.ts    # 命令生成器（已更新）
└── test-ass-subtitles.ts        # 测试文件
```

### 主要类和方法

#### ASSSubtitleUtils 类

```typescript
// 创建ASS字幕文件
static createASSFile(captions, style?, canvasWidth?, canvasHeight?): string

// 颜色格式转换
static convertColorToASS(hexColor: string): string

// 时间格式转换
static convertTimeToASS(timeStr: string): string

// 文本转义
static escapeASSText(text: string): string

// 文件验证
static validateASSFile(filePath: string): boolean
```

#### FFmpegUtils 类（更新）

```typescript
// 创建带样式的字幕文件
static createStyledSubtitleFile(captions, style?, width?, height?): string

// 生成字幕过滤器
static generateSubtitleFilter(subtitlePath, width, height, style?): string
```

## 使用示例

### 基本用法

```typescript
import { ASSSubtitleUtils } from "./utils/assSubtitleUtils";

const captions = [
  {
    id: "1",
    startTime: "00:00:05",
    endTime: "00:00:08",
    text: "Hello World",
  },
];

// 创建基本ASS文件
const assPath = ASSSubtitleUtils.createASSFile(captions);
```

### 带样式的字幕

```typescript
const style = {
  fontSize: 24,
  fontFamily: "Arial",
  fontColor: "#FFFF00",
  strokeWidth: 2,
  strokeColor: "#000000",
  useGradient: true,
  gradientColors: ["#FFFF00", "#FF8800"],
};

const styledAssPath = ASSSubtitleUtils.createASSFile(
  captions,
  style,
  1920,
  1080
);
```

### 在 FFmpeg 命令中使用

```typescript
const generator = new FFmpegCommandGenerator();
const command = await generator.generateCommand({
  width: 1920,
  height: 1080,
  backgroundColor: "#000000",
  elements: [],
  captions: captions,
  globalCaptionStyle: style,
});
```

## ASS 格式特性

### 颜色格式

- 输入: `#RRGGBB` (HTML 格式)
- 输出: `&HBBGGRR&` (ASS 格式)
- 示例: `#FF0000` → `&H0000FF&`

### 时间格式

- 输入: `HH:MM:SS`
- 输出: `H:MM:SS.CC`
- 示例: `00:01:30` → `0:01:30.00`

### 对齐方式

- `left` → 1 (底部左对齐)
- `center` → 2 (底部居中)
- `right` → 3 (底部右对齐)

### 特殊效果

- `\\N` - 强制换行
- `{\\1c&HCOLOR&}` - 主要颜色
- `{\\3c&HCOLOR&}` - 描边颜色
- `{\\shad2}` - 阴影效果

## 配置选项

### ASS_CONFIG 常量

```typescript
export const ASS_CONFIG = {
  DEFAULT_FONT_SIZE_RATIO: 0.035,  // 字号相对画布高度比例
  MIN_FONT_SIZE: 16,               // 最小字号
  DEFAULT_MARGIN_RATIO: 0.02,      // 边距比例
  DEFAULT_STROKE_WIDTH: 2,         // 默认描边宽度
  DEFAULT_SHADOW_BLUR: 2,          // 默认阴影模糊
  ALIGNMENT: { ... },              // 对齐方式映射
  COLORS: { ... }                  // 预定义颜色
};
```

## 测试

运行测试以验证 ASS 字幕功能：

```bash
# 编译TypeScript
npm run build

# 运行测试
node dist/ffmpeg/test-ass-subtitles.js
```

测试包括：

- ✅ 基本 ASS 文件创建
- ✅ 带样式的 ASS 文件创建
- ✅ 颜色转换测试
- ✅ 时间格式转换测试
- ✅ 文本转义测试
- ✅ 文件验证测试

## 兼容性

### FFmpeg 版本要求

- 推荐 FFmpeg 4.0+
- 需要支持 `ass` 过滤器
- 需要支持 `libass` 库

### 字体支持

- 系统字体自动映射
- 支持常见字体族
- 回退到默认字体（Arial）

## 性能优化

### 文件管理

- 自动清理临时文件
- 错误处理和资源释放
- 内存使用优化

### 渲染优化

- 响应式字号计算
- 智能边距设置
- 样式缓存机制

## 故障排除

### 常见问题

1. **字幕不显示**

   - 检查 ASS 文件格式
   - 验证时间格式
   - 确认 FFmpeg 版本

2. **样式不生效**

   - 检查颜色格式转换
   - 验证字体名称
   - 确认样式参数

3. **文件创建失败**
   - 检查临时目录权限
   - 验证输入数据格式
   - 查看错误日志

### 调试方法

```typescript
// 启用详细日志
console.log("Generated ASS content:", assContent);

// 验证文件
const isValid = ASSSubtitleUtils.validateASSFile(filePath);

// 检查字幕数量
const count = ASSSubtitleUtils.getSubtitleCount(filePath);
```

## 未来计划

- [ ] 支持更多 ASS 特效
- [ ] 字幕动画效果
- [ ] 多语言字幕支持
- [ ] 字幕模板系统
- [ ] 实时预览功能

---

**注意**: 此升级向后兼容，现有的字幕数据结构无需修改，系统会自动处理格式转换。

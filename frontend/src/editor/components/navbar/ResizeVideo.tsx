import React, { use<PERSON><PERSON>back, useState } from "react";
import { <PERSON>, <PERSON>con<PERSON>utt<PERSON>, <PERSON><PERSON><PERSON>, Typography } from "@mui/material";
import AspectRatioIcon from "@mui/icons-material/AspectRatio";
import LandscapeIcon from "@mui/icons-material/Landscape";
import {
  Instagram as InstagramIcon,
  MusicVideo as TikTokIcon,
  YouTube as YouTubeIcon,
  OndemandVideo as YouTubeShortIcon,
} from "@mui/icons-material";
import { StoreContext } from "../../../store";
import { CustomPopover } from "..";
import { useLanguage } from "../../../i18n/LanguageContext";

// Common styles
const COMMON_STYLES = {
  flexColumnWithGap: {
    display: "flex",
    flexDirection: "column",
    gap: 2,
  },
};

export interface ResizeValue {
  width: number;
  height: number;
  name: string;
}

export const RESIZE_OPTIONS = [
  {
    label: "original",
    icon: LandscapeIcon,
    value: {
      width: 1280,
      height: 720,
      name: "16:9",
    },
    platforms: "YouTube ads",
  },
  {
    label: "aspect_ratio_4_3",
    icon: AspectRatioIcon,
    value: {
      width: 960,
      height: 720,
      name: "4:3",
    },
    platforms: "LinkedIn ads, Facebook ads",
  },
  {
    label: "aspect_ratio_2_1",
    icon: AspectRatioIcon,
    value: {
      width: 1280,
      height: 640,
      name: "2:1",
    },
    platforms: "",
  },
  {
    label: "aspect_ratio_9_16",
    icon: TikTokIcon,
    value: {
      width: 720,
      height: 1280,
      name: "9:16",
    },
    platforms: "TikTok, TikTok ads",
  },
  {
    label: "aspect_ratio_1_1",
    icon: InstagramIcon,
    value: {
      width: 720,
      height: 720,
      name: "1:1",
    },
    platforms: "Instagram posts",
  },
  {
    label: "aspect_ratio_3_4",
    icon: AspectRatioIcon,
    value: {
      width: 720,
      height: 960,
      name: "3:4",
    },
    platforms: "",
  },
];

interface ResizeOptionProps {
  label: string;
  Icon: React.ElementType;
  value: ResizeValue;
  platforms?: string;
  handleResize: (payload: ResizeValue) => void;
  t: (key: string) => string;
}

const ResizeOption = React.memo(
  ({ label, Icon, value, platforms, handleResize, t }: ResizeOptionProps) => {
    const handleClick = useCallback(() => {
      handleResize(value);
    }, [handleResize, value]);

    return (
      <Box
        onClick={handleClick}
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 2,
          cursor: "pointer",
          p: 1.5,
          borderRadius: 1.5,
          transition: "all 0.2s ease-in-out",
          "&:hover": {
            bgcolor: "action.hover",
            transform: "translateY(-2px)",
            boxShadow: (theme) => `0 4px 12px ${theme.palette.action.hover}`,
          },
        }}
      >
        <Box
          sx={{
            width: 38,
            height: 38,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            borderRadius: "50%",
            bgcolor: (theme) => theme.palette.primary.light,
            color: (theme) => theme.palette.primary.contrastText,
          }}
        >
          <Icon />
        </Box>
        <Box sx={{ flex: 1 }}>
          <Typography variant="subtitle2" fontWeight="medium">
            {value.name}
          </Typography>
          {platforms && (
            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: "block", mt: 0.5 }}
            >
              {platforms}
            </Typography>
          )}
        </Box>
      </Box>
    );
  }
);

ResizeOption.displayName = "ResizeOption";

export const ResizeVideo = React.memo(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  const handleResize = useCallback(
    (payload: ResizeValue) => {
      store.setCanvasSize(payload.width, payload.height);
      store.saveChange();
    },
    [store]
  );

  return (
    <CustomPopover
      customTrigger={
        <Tooltip title={t("ratio")} arrow>
          <IconButton>
            <AspectRatioIcon sx={{ fontSize: 22 }} />
          </IconButton>
        </Tooltip>
      }
      minWidth={260}
      popoverProps={{
        sx: {
          "& .MuiPaper-root": {
            p: 1.5,
            boxShadow: (theme) => theme.shadows[4],
          },
        },
      }}
    >
      <Typography variant="body2" sx={{ mb: 2, px: 1 }}>
        {t("ratio")}
      </Typography>
      <Box sx={{ ...COMMON_STYLES.flexColumnWithGap, gap: 1 }}>
        {RESIZE_OPTIONS.map((option, index) => (
          <ResizeOption
            key={index}
            label={option.label}
            Icon={option.icon}
            value={option.value}
            platforms={option.platforms}
            handleResize={handleResize}
            t={t}
          />
        ))}
      </Box>
    </CustomPopover>
  );
});

ResizeVideo.displayName = "ResizeVideo";

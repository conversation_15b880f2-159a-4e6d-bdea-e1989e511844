import React, { useState, useEffect } from "react";
import { observer } from "mobx-react";
import {
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  CircularProgress,
  Rating,
  Tooltip,
  Fab,
  Paper,
  alpha,
} from "@mui/material";
import {
  Search as SearchIcon,
  PlayArrow as PlayIcon,
  Add as AddIcon,
  Favorite as FavoriteIcon,
  AccessTime as TimeIcon,
  Visibility as ViewIcon,
  Star as StarIcon,
  FilterList as FilterIcon,
} from "@mui/icons-material";
import { useStore } from "../../store";
import { Template, TemplateCategory, TemplateFilter } from "../../types";
import {
  getCategoryDisplayName,
  getTemplateCategories,
} from "../../services/templateService";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`template-tabpanel-${index}`}
      aria-labelledby={`template-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
}

const Templates: React.FC = observer(() => {
  const store = useStore();
  const templateManager = store.templateManager;

  // 本地状态
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<
    TemplateCategory | "all"
  >("all");
  const [tabValue, setTabValue] = useState(0);
  const [previewTemplate, setPreviewTemplate] = useState<Template | null>(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newTemplateName, setNewTemplateName] = useState("");
  const [newTemplateDescription, setNewTemplateDescription] = useState("");
  const [newTemplateCategory, setNewTemplateCategory] =
    useState<TemplateCategory>("other");
  const [newTemplateTags, setNewTemplateTags] = useState("");

  // 组件挂载时加载数据
  useEffect(() => {
    templateManager.loadTemplates();
    templateManager.loadPopularTemplates();
    templateManager.loadLatestTemplates();
  }, [templateManager]);

  // 搜索处理
  const handleSearch = () => {
    const filter: TemplateFilter = {};
    if (selectedCategory !== "all") {
      filter.category = selectedCategory;
    }

    if (searchQuery.trim()) {
      templateManager.searchTemplates(searchQuery, filter);
    } else {
      templateManager.loadTemplates(filter);
    }
  };

  // 分类过滤
  const handleCategoryChange = (category: TemplateCategory | "all") => {
    setSelectedCategory(category);
    const filter: TemplateFilter = {};
    if (category !== "all") {
      filter.category = category;
    }
    templateManager.loadTemplates(filter);
  };

  // 应用模板
  const handleApplyTemplate = async (template: Template) => {
    const success = await templateManager.applyTemplate(template);
    if (success) {
      // 可以显示成功消息
      console.log("模板应用成功");
    }
  };

  // 预览模板
  const handlePreviewTemplate = (template: Template) => {
    setPreviewTemplate(template);
  };

  // 创建模板
  const handleCreateTemplate = async () => {
    if (!newTemplateName.trim()) return;

    try {
      const tags = newTemplateTags
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag);
      await templateManager.createTemplateFromCurrentProject(
        newTemplateName,
        newTemplateDescription,
        newTemplateCategory,
        tags,
        true
      );

      // 重置表单
      setNewTemplateName("");
      setNewTemplateDescription("");
      setNewTemplateCategory("other");
      setNewTemplateTags("");
      setCreateDialogOpen(false);

      // 重新加载模板列表
      templateManager.loadTemplates();
    } catch (error) {
      console.error("创建模板失败:", error);
    }
  };

  // 格式化时长
  const formatDuration = (duration: number) => {
    const seconds = Math.floor(duration / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // 渲染模板卡片
  const renderTemplateCard = (template: Template) => (
    <Grid item xs={12} sm={6} md={6} lg={12} key={template.id}>
      <Card
        sx={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          transition: "all 0.3s ease-in-out",
          "&:hover": {
            transform: "translateY(-4px)",
            boxShadow: 6,
          },
          overflow: "hidden",
          borderRadius: 2,
        }}
      >
        <CardMedia
          component="div"
          sx={{
            height: 160,
            cursor: "pointer",
            backgroundSize: "cover",
            backgroundPosition: "center",
            position: "relative",
            "&::before": {
              content: '""',
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: "rgba(0,0,0,0.1)",
            },
          }}
          style={{
            backgroundImage: `url(${
              template.thumbnail || "/placeholder-template.png"
            })`,
          }}
          onClick={() => handlePreviewTemplate(template)}
        />
        <CardContent sx={{ flexGrow: 1, p: 2 }}>
          <Tooltip title={template.name} placement="top">
            <Typography
              gutterBottom
              variant="h6"
              component="div"
              noWrap
              sx={{
                fontWeight: 600,
                mb: 0.5,
              }}
            >
              {template.name}
            </Typography>
          </Tooltip>

          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              mb: 1.5,
              display: "-webkit-box",
              WebkitLineClamp: 2,
              WebkitBoxOrient: "vertical",
              overflow: "hidden",
              height: 40,
            }}
          >
            {template.description}
          </Typography>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              gap: 1,
              mb: 1,
              flexWrap: "wrap",
            }}
          >
            <Chip
              label={getCategoryDisplayName(template.category)}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ height: 22 }}
            />
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 0.5,
                bgcolor: (theme) => alpha(theme.palette.primary.main, 0.1),
                borderRadius: 1,
                px: 0.8,
                py: 0.3,
              }}
            >
              <TimeIcon sx={{ fontSize: 14 }} />
              <Typography variant="caption" sx={{ fontWeight: 500 }}>
                {formatDuration(template.metadata.duration)}
              </Typography>
            </Box>
          </Box>

          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "space-between",
              mt: 1,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <Rating
                value={template.rating}
                readOnly
                size="small"
                precision={0.5}
              />
              <Typography variant="caption" color="text.secondary">
                ({template.ratingCount})
              </Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
              <ViewIcon sx={{ fontSize: 16, color: "text.secondary" }} />
              <Typography variant="caption" color="text.secondary">
                {template.usageCount.toLocaleString()}
              </Typography>
            </Box>
          </Box>
        </CardContent>

        <CardActions sx={{ px: 2, pb: 2, pt: 0 }}>
          <Button
            size="small"
            startIcon={<PlayIcon />}
            onClick={() => handlePreviewTemplate(template)}
            sx={{
              borderRadius: 1.5,
              textTransform: "none",
            }}
          >
            预览
          </Button>
          <Button
            size="small"
            variant="contained"
            onClick={() => handleApplyTemplate(template)}
            disabled={templateManager.isApplying}
            sx={{
              ml: "auto",
              borderRadius: 1.5,
              textTransform: "none",
              boxShadow: "none",
              "&:hover": { boxShadow: 2 },
            }}
          >
            应用
          </Button>
        </CardActions>
      </Card>
    </Grid>
  );

  return (
    <Box
      sx={{
        height: "100%",
        display: "flex",
        flexDirection: "column",
        bgcolor: (theme) =>
          theme.palette.mode === "dark" ? "#1A1A1A" : "#F8F9FA",
      }}
    >
      {/* 搜索和过滤 */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: "divider",
          borderRadius: 0,
          bgcolor: (theme) => theme.palette.background.paper,
        }}
      >
        <Box
          sx={{
            display: "flex",
            gap: 2,
            flexWrap: { xs: "wrap", sm: "nowrap" },
          }}
        >
          <TextField
            fullWidth
            placeholder="搜索模板..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onKeyPress={(e) => e.key === "Enter" && handleSearch()}
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{
              flexGrow: 1,
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
              },
            }}
          />

          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel>分类</InputLabel>
            <Select
              value={selectedCategory}
              label="分类"
              onChange={(e) =>
                handleCategoryChange(e.target.value as TemplateCategory | "all")
              }
              sx={{ borderRadius: 2 }}
              startAdornment={
                <InputAdornment position="start">
                  <FilterIcon fontSize="small" />
                </InputAdornment>
              }
            >
              <MenuItem value="all">全部分类</MenuItem>
              {getTemplateCategories().map((category) => (
                <MenuItem key={category} value={category}>
                  {getCategoryDisplayName(category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      </Paper>

      {/* 标签页 */}
      <Box
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          bgcolor: "background.paper",
        }}
      >
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => setTabValue(newValue)}
          variant="fullWidth"
          sx={{
            "& .MuiTab-root": {
              textTransform: "none",
              fontWeight: 500,
              fontSize: "0.95rem",
            },
          }}
        >
          <Tab label="全部模板" />
          <Tab label="热门模板" />
          <Tab label="最新模板" />
        </Tabs>
      </Box>

      {/* 内容区域 */}
      <Box sx={{ flexGrow: 1, overflow: "auto", px: 2, py: 3 }}>
        <TabPanel value={tabValue} index={0}>
          {templateManager.isLoading ? (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                height: "100%",
                minHeight: 200,
              }}
            >
              <CircularProgress color="primary" />
            </Box>
          ) : templateManager.templates.length > 0 ? (
            <Grid container spacing={3}>
              {templateManager.templates.map(renderTemplateCard)}
            </Grid>
          ) : (
            <Box sx={{ textAlign: "center", py: 6 }}>
              <Typography variant="h6" color="text.secondary">
                暂无模板
              </Typography>
              <Typography variant="body2" color="text.secondary">
                尝试更改搜索条件或创建新模板
              </Typography>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {templateManager.popularTemplates.length > 0 ? (
            <Grid container spacing={3}>
              {templateManager.popularTemplates.map(renderTemplateCard)}
            </Grid>
          ) : (
            <Box sx={{ textAlign: "center", py: 6 }}>
              <Typography variant="h6" color="text.secondary">
                暂无热门模板
              </Typography>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {templateManager.latestTemplates.length > 0 ? (
            <Grid container spacing={3}>
              {templateManager.latestTemplates.map(renderTemplateCard)}
            </Grid>
          ) : (
            <Box sx={{ textAlign: "center", py: 6 }}>
              <Typography variant="h6" color="text.secondary">
                暂无最新模板
              </Typography>
            </Box>
          )}
        </TabPanel>
      </Box>

      {/* 创建模板按钮 */}
      <Tooltip title="创建新模板">
        <Fab
          color="primary"
          aria-label="创建模板"
          sx={{
            position: "fixed",
            bottom: 24,
            right: 24,
            boxShadow: 3,
          }}
          onClick={() => setCreateDialogOpen(true)}
        >
          <AddIcon />
        </Fab>
      </Tooltip>

      {/* 模板预览对话框 */}
      <Dialog
        open={!!previewTemplate}
        onClose={() => setPreviewTemplate(null)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 },
        }}
      >
        {previewTemplate && (
          <>
            <DialogTitle
              sx={{ px: 3, py: 2, borderBottom: 1, borderColor: "divider" }}
            >
              <Typography variant="h6" component="div">
                {previewTemplate.name}
              </Typography>
            </DialogTitle>
            <DialogContent sx={{ p: 0 }}>
              <Box sx={{ position: "relative" }}>
                <img
                  src={previewTemplate.thumbnail || "/placeholder-template.png"}
                  alt={previewTemplate.name}
                  style={{
                    width: "100%",
                    height: 300,
                    objectFit: "cover",
                  }}
                />
              </Box>
              <Box sx={{ p: 3 }}>
                <Typography variant="body1" sx={{ mb: 2 }}>
                  {previewTemplate.description}
                </Typography>
                <Box sx={{ display: "flex", gap: 1, flexWrap: "wrap", mb: 2 }}>
                  {previewTemplate.tags.map((tag) => (
                    <Chip key={tag} label={tag} size="small" />
                  ))}
                </Box>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 0.5 }}>
                    <Rating
                      value={previewTemplate.rating}
                      readOnly
                      precision={0.5}
                    />
                    <Typography variant="body2" color="text.secondary">
                      ({previewTemplate.ratingCount})
                    </Typography>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                    <Box
                      sx={{ display: "flex", alignItems: "center", gap: 0.5 }}
                    >
                      <ViewIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {previewTemplate.usageCount.toLocaleString()} 次使用
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </Box>
            </DialogContent>
            <DialogActions
              sx={{ px: 3, py: 2, borderTop: 1, borderColor: "divider" }}
            >
              <Button
                onClick={() => setPreviewTemplate(null)}
                variant="outlined"
                sx={{ borderRadius: 1.5, textTransform: "none" }}
              >
                取消
              </Button>
              <Button
                variant="contained"
                onClick={() => {
                  handleApplyTemplate(previewTemplate);
                  setPreviewTemplate(null);
                }}
                disabled={templateManager.isApplying}
                sx={{
                  borderRadius: 1.5,
                  textTransform: "none",
                  boxShadow: "none",
                }}
              >
                {templateManager.isApplying ? (
                  <>
                    <CircularProgress size={16} sx={{ mr: 1 }} />
                    应用中
                  </>
                ) : (
                  "应用模板"
                )}
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* 创建模板对话框 */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: 2 },
        }}
      >
        <DialogTitle
          sx={{ px: 3, py: 2, borderBottom: 1, borderColor: "divider" }}
        >
          <Typography variant="h6">创建模板</Typography>
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          <TextField
            fullWidth
            label="模板名称"
            value={newTemplateName}
            onChange={(e) => setNewTemplateName(e.target.value)}
            sx={{ mb: 2, mt: 1 }}
            InputProps={{
              sx: { borderRadius: 1.5 },
            }}
          />
          <TextField
            fullWidth
            label="模板描述"
            multiline
            rows={3}
            value={newTemplateDescription}
            onChange={(e) => setNewTemplateDescription(e.target.value)}
            sx={{ mb: 2 }}
            InputProps={{
              sx: { borderRadius: 1.5 },
            }}
          />
          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>分类</InputLabel>
            <Select
              value={newTemplateCategory}
              label="分类"
              onChange={(e) =>
                setNewTemplateCategory(e.target.value as TemplateCategory)
              }
              sx={{ borderRadius: 1.5 }}
            >
              {getTemplateCategories().map((category) => (
                <MenuItem key={category} value={category}>
                  {getCategoryDisplayName(category)}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="标签 (用逗号分隔)"
            value={newTemplateTags}
            onChange={(e) => setNewTemplateTags(e.target.value)}
            placeholder="例如: 商务, 演示, 动画"
            InputProps={{
              sx: { borderRadius: 1.5 },
            }}
          />
        </DialogContent>
        <DialogActions
          sx={{ px: 3, py: 2, borderTop: 1, borderColor: "divider" }}
        >
          <Button
            onClick={() => setCreateDialogOpen(false)}
            variant="outlined"
            sx={{ borderRadius: 1.5, textTransform: "none" }}
          >
            取消
          </Button>
          <Button
            variant="contained"
            onClick={handleCreateTemplate}
            disabled={!newTemplateName.trim() || templateManager.isCreating}
            sx={{ borderRadius: 1.5, textTransform: "none", boxShadow: "none" }}
          >
            {templateManager.isCreating ? (
              <>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                创建中
              </>
            ) : (
              "创建"
            )}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
});

export default Templates;

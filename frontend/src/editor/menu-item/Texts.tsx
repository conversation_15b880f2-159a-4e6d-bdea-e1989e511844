"use client";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  TextField,
  InputAdornment,
  IconButton,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import ClearIcon from "@mui/icons-material/Clear";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import { useLanguage } from "../../i18n/LanguageContext";

// 文字样式预设
const getTextResources = (t: (key: string) => string) => [
  // 标题类
  {
    name: t("text_title"),
    fontSize: 60,
    fontWeight: 700,
    fontFamily: "Roboto",
    category: "title",
    color: "#ffffff",
    description: t("text_desc_main_title"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 1,
    strokeColor: "#00ff00",
    backgroundColor: "#ff00ff",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: t("text_subtitle"),
    fontSize: 36,
    fontWeight: 600,
    fontFamily: "Roboto",
    category: "title",
    color: "#ffffff",
    description: t("text_desc_subtitle"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 0,
  },
  {
    name: "影片标题",
    fontSize: 68,
    fontWeight: 800,
    fontFamily: "Times New Roman",
    category: "title",
    color: "#ffffff",
    description: "电影级别的主标题，庄重大气",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 6,
    shadowColor: "rgba(0,0,0,0.8)",
    strokeWidth: 1,
    strokeColor: "#cccccc",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 0.95,
    charSpacing: 3,
  },
  {
    name: "章节标题",
    fontSize: 44,
    fontWeight: 700,
    fontFamily: "Arial",
    category: "title",
    color: "#ffd700",
    description: "清晰的章节分割标题",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "rgba(0,0,0,0.7)",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 2,
  },
  {
    name: "片尾字幕",
    fontSize: 36,
    fontWeight: 400,
    fontFamily: "Helvetica",
    category: "title",
    color: "#e0e0e0",
    description: "优雅的片尾字幕样式",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.5)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.4,
    charSpacing: 4,
  },
  {
    name: "品牌标题",
    fontSize: 52,
    fontWeight: 600,
    fontFamily: "Segoe UI",
    category: "title",
    color: "#2196f3",
    description: "现代感品牌标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 4,
    shadowColor: "rgba(33,150,243,0.3)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  // 字幕类
  {
    name: "标准字幕",
    fontSize: 30,
    fontWeight: 500,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "清晰易读的通用字幕",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.8)",
    strokeWidth: 1,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "电影字幕",
    fontSize: 26,
    fontWeight: 400,
    fontFamily: "Lato",
    category: "subtitle",
    color: "#ffffff",
    description: "电影院风格字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(0,0,0,0.75)",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  {
    name: "新闻字幕",
    fontSize: 28,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "新闻播报专用字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#1565c0",
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "纪录片字幕",
    fontSize: 24,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "subtitle",
    color: "#f8f8f8",
    description: "纪录片专业字幕",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "rgba(0,0,0,0.6)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.5,
    charSpacing: 0,
  },
  {
    name: "双语字幕",
    fontSize: 22,
    fontWeight: 500,
    fontFamily: "Arial",
    category: "subtitle",
    color: "#ffffff",
    description: "中英双语字幕样式",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.9)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(0,0,0,0.4)",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  // 正文类
  {
    name: t("text_body"),
    fontSize: 24,
    fontWeight: 400,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_body"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  {
    name: t("text_caption"),
    fontSize: 18,
    fontWeight: 300,
    fontFamily: "Roboto",
    category: "body",
    color: "#ffffff",
    description: t("text_desc_caption"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "教学说明",
    fontSize: 20,
    fontWeight: 500,
    fontFamily: "Roboto",
    category: "body",
    color: "#2196f3",
    description: "教育视频中的说明文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 1,
    shadowColor: "#ffffff",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(255,255,255,0.9)",
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.4,
    charSpacing: 0,
  },
  // 标注类
  {
    name: "重点标注",
    fontSize: 34,
    fontWeight: 700,
    fontFamily: "Roboto",
    category: "annotation",
    color: "#ff3030",
    description: "突出重要信息的标注",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "rgba(0,0,0,0.5)",
    strokeWidth: 2,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "警告提示",
    fontSize: 26,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "annotation",
    color: "#ffffff",
    description: "警告和提示标签",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ff9800",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 0,
  },
  {
    name: "位置标记",
    fontSize: 20,
    fontWeight: 500,
    fontFamily: "Roboto",
    category: "annotation",
    color: "#ffffff",
    description: "地点和位置标记",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.6)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#4caf50",
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 0,
  },
  {
    name: "时间戳",
    fontSize: 22,
    fontWeight: 400,
    fontFamily: "Courier New",
    category: "annotation",
    color: "#ffd54f",
    description: "时间信息显示",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.8)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(0,0,0,0.7)",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "说明文字",
    fontSize: 18,
    fontWeight: 500,
    fontFamily: "Helvetica",
    category: "annotation",
    color: "#333333",
    description: "解释和说明文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(255,255,255,0.9)",
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  // 社交媒体类
  {
    name: "抖音标题",
    fontSize: 44,
    fontWeight: 800,
    fontFamily: "PingFang SC",
    category: "social",
    color: "#ff0050",
    description: "抖音风格动感标题",
    shadowBlur: 0,
    shadowOffsetX: 3,
    shadowOffsetY: 3,
    shadowColor: "#00f2ea",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 0.95,
    charSpacing: 2,
  },
  {
    name: "小红书风格",
    fontSize: 38,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "social",
    color: "#ffffff",
    description: "小红书时尚文字",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#ff2442",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 1,
  },
  {
    name: "B站标题",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Microsoft YaHei",
    category: "social",
    color: "#ffffff",
    description: "B站风格标题",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 3,
    shadowColor: "rgba(0,0,0,0.7)",
    strokeWidth: 2,
    strokeColor: "#00a1d6",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "YouTube缩略图",
    fontSize: 46,
    fontWeight: 900,
    fontFamily: "Roboto",
    category: "social",
    color: "#ffffff",
    description: "YouTube缩略图专用",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 3,
    strokeColor: "#000000",
    backgroundColor: "#ff0000",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 0.9,
    charSpacing: 1,
  },
  {
    name: "微博热搜",
    fontSize: 34,
    fontWeight: 600,
    fontFamily: "Microsoft YaHei",
    category: "social",
    color: "#ff6900",
    description: "微博热搜标题风格",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "rgba(0,0,0,0.5)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "rgba(255,255,255,0.9)",
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 0,
  },

  // 创意字体类
  {
    name: "Creative Title",
    fontSize: 48,
    fontWeight: 700,
    fontFamily: "Impact",
    category: "creative",
    color: "#ff6b35",
    description: t("text_desc_creative_title"),
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 2,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 2,
  },
  {
    name: "Elegant Title",
    fontSize: 42,
    fontWeight: 400,
    fontFamily: "Georgia",
    category: "creative",
    color: "#8e44ad",
    description: t("text_desc_elegant_title"),
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  {
    name: "Tech Style",
    fontSize: 32,
    fontWeight: 500,
    fontFamily: "Courier New",
    category: "creative",
    color: "#00d4aa",
    description: t("text_desc_tech"),
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 3,
  },
  {
    name: "Classic Serif",
    fontSize: 38,
    fontWeight: 400,
    fontFamily: "Times New Roman",
    category: "creative",
    color: "#2c3e50",
    description: t("text_desc_classic_serif"),
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#666666",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "手写风格",
    fontSize: 34,
    fontWeight: 400,
    fontFamily: "Comic Sans MS",
    category: "creative",
    color: "#795548",
    description: "手写字体效果，温馨自然",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "left" as "left" | "center" | "right",
    lineHeight: 1.3,
    charSpacing: 0,
  },
  {
    name: "艺术字体",
    fontSize: 46,
    fontWeight: 300,
    fontFamily: "Comic Sans MS",
    category: "creative",
    color: "#e91e63",
    description: "优雅的艺术字体效果",
    shadowBlur: 0,
    shadowOffsetX: 1,
    shadowOffsetY: 1,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  // 特效样式类
  {
    name: "Neon Glow",
    fontSize: 44,
    fontWeight: 600,
    fontFamily: "Arial",
    category: "effects",
    color: "#00ffff",
    description: "Neon Glow",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#00ffff",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 4,
  },
  {
    name: "Retro Style",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Impact",
    category: "effects",
    color: "#ff4757",
    description: "Retro Style",
    shadowBlur: 0,
    shadowOffsetX: 3,
    shadowOffsetY: 3,
    shadowColor: "#8B0000",
    strokeWidth: 3,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 2,
  },
  {
    name: "Modern Clean",
    fontSize: 36,
    fontWeight: 300,
    fontFamily: "Arial",
    category: "effects",
    color: "#ffffff",
    description: "Modern Clean",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "#000000",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: "#2f3542",
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.2,
    charSpacing: 1,
  },
  {
    name: "Playful Fun",
    fontSize: 46,
    fontWeight: 400,
    fontFamily: "Comic Sans MS",
    category: "effects",
    color: "#ff9ff3",
    description: "Playful Fun",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#ff1493",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 1,
  },
  {
    name: "霓虹发光",
    fontSize: 42,
    fontWeight: 600,
    fontFamily: "Courier New",
    category: "effects",
    color: "#00ffff",
    description: "科技感霓虹发光效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#00ffff",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 4,
  },
  {
    name: "3D立体",
    fontSize: 48,
    fontWeight: 800,
    fontFamily: "Impact",
    category: "effects",
    color: "#ffffff",
    description: "立体浮雕文字效果",
    shadowBlur: 0,
    shadowOffsetX: 5,
    shadowOffsetY: 5,
    shadowColor: "#888888",
    strokeWidth: 2,
    strokeColor: "#dddddd",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 0.9,
    charSpacing: 2,
  },
  {
    name: "火焰特效",
    fontSize: 46,
    fontWeight: 700,
    fontFamily: "Impact",
    category: "effects",
    color: "#ff4500",
    description: "炽热火焰燃烧效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#ff0000",
    strokeWidth: 1,
    strokeColor: "#ffff00",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 3,
  },
  {
    name: "冰霜特效",
    fontSize: 42,
    fontWeight: 600,
    fontFamily: "Helvetica",
    category: "effects",
    color: "#87ceeb",
    description: "冰冷霜雪文字效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 0,
    shadowColor: "#ffffff",
    strokeWidth: 2,
    strokeColor: "#b0e0e6",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 2,
  },
  {
    name: "金属质感",
    fontSize: 40,
    fontWeight: 700,
    fontFamily: "Arial Black",
    category: "effects",
    color: "#c0c0c0",
    description: "金属光泽反射效果",
    shadowBlur: 0,
    shadowOffsetX: 2,
    shadowOffsetY: 2,
    shadowColor: "#404040",
    strokeWidth: 1,
    strokeColor: "#ffffff",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.0,
    charSpacing: 1,
  },
  {
    name: "渐变彩虹",
    fontSize: 44,
    fontWeight: 600,
    fontFamily: "Verdana",
    category: "effects",
    color: "#ff69b4",
    description: "彩虹渐变炫彩效果",
    shadowBlur: 0,
    shadowOffsetX: 0,
    shadowOffsetY: 2,
    shadowColor: "rgba(0,0,0,0.3)",
    strokeWidth: 0,
    strokeColor: "#000000",
    backgroundColor: undefined,
    textAlign: "center" as "left" | "center" | "right",
    lineHeight: 1.1,
    charSpacing: 2,
  },
];

export const Texts = observer(() => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const TEXT_RESOURCES = getTextResources(t);
  const [searchTerm, setSearchTerm] = React.useState("");

  // 过滤和分组文字样式
  const groupedResources = React.useMemo(() => {
    const filteredResources = TEXT_RESOURCES.filter(
      (resource) =>
        resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        resource.description.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const groups: { [key: string]: typeof TEXT_RESOURCES } = {};
    filteredResources.forEach((resource) => {
      if (!groups[resource.category]) {
        groups[resource.category] = [];
      }
      groups[resource.category].push(resource);
    });
    return groups;
  }, [TEXT_RESOURCES, searchTerm]);

  // 添加文字的处理函数
  const handleAddText = (resource: (typeof TEXT_RESOURCES)[0]) => {
    // 直接在addText中传递所有属性，包括阴影、描边、背景色、对齐、行高和字符间距属性
    store.addText({
      text: resource.name,
      fontSize: resource.fontSize,
      fontWeight: resource.fontWeight,
      fontFamily: resource.fontFamily,
      fontColor: resource.color,
      shadowBlur: resource.shadowBlur,
      shadowOffsetX: resource.shadowOffsetX,
      shadowOffsetY: resource.shadowOffsetY,
      shadowColor: resource.shadowColor,
      strokeWidth: resource.strokeWidth,
      strokeColor: resource.strokeColor,
      backgroundColor: resource.backgroundColor,
      textAlign: resource.textAlign,
      lineHeight: resource.lineHeight,
      charSpacing: resource.charSpacing,
    });

    store.saveChange();
  };

  // 渲染文字样式按钮
  const renderTextButton = (resource: (typeof TEXT_RESOURCES)[0]) => {
    const isLightColor = (color: string) => {
      const lightColors = ["#ffffff", "#00ffff", "#ff9ff3"];
      return lightColors.includes(color.toLowerCase());
    };

    return (
      <Button
        key={resource.name}
        onClick={() => handleAddText(resource)}
        variant="outlined"
        fullWidth
        sx={{
          textTransform: "none",
          minHeight: 80,
          p: 2.5,
          border: "1.5px solid",
          borderColor: "divider",
          background: "grey.50",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          justifyContent: "center",
          borderRadius: 3,
          overflow: "hidden",
          fontFamily:
            "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
          "&:hover": {
            transform: "translateY(-6px)",
            boxShadow: "0 12px 30px rgba(0,0,0,0.18)",
            borderColor: "primary.main",
            background: "background.paper",
          },
          "&:active": {
            transform: "translateY(-3px)",
          },
          transition: "all 0.35s cubic-bezier(0.4, 0, 0.2, 1)",
        }}
      >
        <Typography
          sx={{
            // 使用适合预览的字体大小，保持比例关系
            fontSize: Math.min(resource.fontSize * 0.3, 18),
            fontWeight: resource.fontWeight,
            fontFamily: resource.fontFamily,
            color: resource.color,
            // 使用资源中定义的行高，与画布保持一致
            lineHeight: resource.lineHeight,
            mb: 0.8,
            // 使用资源中定义的文本对齐方式
            textAlign: resource.textAlign,
            width: "100%",
            // 使用资源中定义的字符间距，按比例缩放
            letterSpacing: `${resource.charSpacing * 0.3}px`,
            // 合并阴影和描边效果，使用外部描边与后端保持一致
            textShadow: (() => {
              const shadows = [];

              // 添加阴影效果
              if (
                resource.shadowOffsetX !== 0 ||
                resource.shadowOffsetY !== 0
              ) {
                shadows.push(
                  `${resource.shadowOffsetX * 0.28}px ${
                    resource.shadowOffsetY * 0.28
                  }px 0px ${resource.shadowColor}`
                );
              } else if (isLightColor(resource.color)) {
                shadows.push("0.8px 0.8px 0px rgba(0,0,0,0.8)");
              }

              // 添加外部描边效果
              if (resource.strokeWidth > 0) {
                const strokeWidth = Math.max(resource.strokeWidth * 0.28, 0.4);
                shadows.push(
                  `${strokeWidth}px 0px 0px ${resource.strokeColor}`,
                  `-${strokeWidth}px 0px 0px ${resource.strokeColor}`,
                  `0px ${strokeWidth}px 0px ${resource.strokeColor}`,
                  `0px -${strokeWidth}px 0px ${resource.strokeColor}`
                );
              }

              return shadows.length > 0 ? shadows.join(", ") : "none";
            })(),
            // 背景色效果，与画布中的显示保持一致
            backgroundColor: resource.backgroundColor || "transparent",
            padding: resource.backgroundColor ? "2px 6px" : "0",
            borderRadius: resource.backgroundColor ? "3px" : "0",
            // 确保文字在容器中正确显示
            display: "block",
            overflow: "hidden",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            // 改善文字渲染质量
            "-webkit-font-smoothing": "antialiased",
            "-moz-osx-font-smoothing": "grayscale",
          }}
        >
          {resource.name}
        </Typography>
        <Typography
          variant="caption"
          sx={{
            color: "text.secondary",
            fontSize: "0.75rem",
            textAlign: "left",
            width: "100%",
            fontWeight: 500,
            lineHeight: 1.4,
            mt: 0.5,
            fontFamily:
              "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
          }}
        >
          {resource.description}
        </Typography>
      </Button>
    );
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "background.paper",
        borderRadius: 1,
        boxShadow: 1,
      }}
    >
      {/* 标题栏 */}
      <Box
        sx={{
          bgcolor: "grey.100",
          minHeight: 48,
          display: "flex",
          flexDirection: "column",
          px: 2,
          py: 1.5,
          flexShrink: 0,
          borderBottom: 1,
          borderColor: "divider",
        }}
      >
        <Typography
          variant="h6"
          sx={{
            mb: 1,
            fontWeight: 700,
            fontSize: "1.1rem",
            color: "text.primary",
            letterSpacing: "0.5px",
          }}
        >
          {t("Text")}
        </Typography>

        {/* 搜索框 */}
        <TextField
          size="small"
          placeholder={t("text_search_placeholder")}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon fontSize="small" />
              </InputAdornment>
            ),
            endAdornment: searchTerm && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchTerm("")}
                  edge="end"
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              fontSize: "0.9rem",
              fontWeight: 400,
              "& fieldset": {
                borderColor: "divider",
                borderRadius: "8px",
              },
              "&:hover fieldset": {
                borderColor: "primary.main",
              },
              "&.Mui-focused fieldset": {
                borderColor: "primary.main",
                borderWidth: "2px",
              },
            },
            "& .MuiInputBase-input": {
              fontSize: "0.9rem",
              fontWeight: 400,
              color: "text.primary",
            },
            "& .MuiInputBase-input::placeholder": {
              fontSize: "0.85rem",
              color: "text.secondary",
              opacity: 0.7,
            },
          }}
        />
      </Box>

      {/* 内容区域 */}
      <Box
        sx={{
          flex: 1,
          overflow: "auto",
          p: 2,
          "&::-webkit-scrollbar": {
            width: "8px",
          },
          "&::-webkit-scrollbar-track": {
            bgcolor: "grey.100",
          },
          "&::-webkit-scrollbar-thumb": {
            bgcolor: "grey.400",
            borderRadius: "4px",
          },
        }}
      >
        {Object.keys(groupedResources).length === 0 ? (
          // 空状态
          <Box
            sx={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              py: 6,
              textAlign: "center",
            }}
          >
            <SearchIcon sx={{ fontSize: 48, color: "text.disabled", mb: 2 }} />
            <Typography
              variant="h6"
              sx={{
                color: "text.secondary",
                fontWeight: 600,
                fontSize: "1.1rem",
                mb: 1,
              }}
            >
              {t("text_no_results")}
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: "text.disabled",
                fontSize: "0.9rem",
                fontWeight: 400,
                lineHeight: 1.5,
              }}
            >
              {t("text_try_different_keywords")}
            </Typography>
          </Box>
        ) : (
          <Stack spacing={3}>
            {Object.entries(groupedResources).map(([category, resources]) => (
              <Box key={category}>
                {/* 类别标题 */}
                <Box sx={{ mb: 2 }}>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      color: "text.primary",
                      fontWeight: 700,
                      fontSize: "0.9rem",
                      textTransform: "uppercase",
                      letterSpacing: "1.5px",
                      mb: 1.5,
                      fontFamily:
                        "Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
                    }}
                  >
                    {category === "title" && t("text_category_title")}
                    {category === "subtitle" && "字幕样式"}
                    {category === "body" && t("text_category_body")}
                    {category === "annotation" && "标注样式"}
                    {category === "social" && "社交媒体"}
                    {category === "creative" && t("text_category_creative")}
                    {category === "effects" && t("text_category_effects")}
                  </Typography>
                  <Divider />
                </Box>

                {/* 文字样式网格 */}
                <Grid container spacing={1.5}>
                  {resources.map((resource) => (
                    <Grid item xs={12} sm={6} key={resource.name}>
                      {renderTextButton(resource)}
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ))}
          </Stack>
        )}
      </Box>
    </Box>
  );
});

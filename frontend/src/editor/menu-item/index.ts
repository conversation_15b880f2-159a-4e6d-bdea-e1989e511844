/**
 * 菜单项组件导出
 *
 * 国际化集成说明：
 * 所有菜单项组件都应支持国际化，步骤如下：
 * 1. 导入 useLanguage hook: import { useLanguage } from "../../i18n/LanguageContext";
 * 2. 在组件中使用: const { t } = useLanguage();
 * 3. 包装所有文本: {t("translation_key")}
 * 4. 确保在 /i18n/locales/zh.ts 和 /i18n/locales/en.ts 中添加相应的翻译键
 *
 * 详细说明请参考：/i18n/i18n-integration-guide.md
 */

// 导出所有菜单项组件
export { Audios } from "./Audios";
export { default as Captions } from "./Captions";
export { Videos } from "./Videos";
export { Images } from "./Images";
export { MenuItem } from "./MenuItems";
export { ElementsPanel } from "./ElementsPanel";
export { Shapes } from "./Shapes";
export { Texts } from "./Texts";
export { Uploads } from "./Uploads";
export { default as Templates } from "./Templates";

// 注意：请根据各组件的实际导出方式进行调整
// 例如：如果组件使用 export default 则用 export { default as Name }
// 如果组件使用 export const Name 则用 export { Name }

"use client";

import { StoreContext } from "../../store";
import { formatTimeToMinSecMili } from "../../utils";
import { observer } from "mobx-react-lite";
import { useContext, useEffect } from "react";
import {
  PlayArrow,
  Pause,
  ZoomIn,
  ZoomOut,
  Delete,
  CenterFocusStrong,
  SkipNext,
  SkipPrevious,
  ContentCut,
} from "@mui/icons-material";
import {
  Box,
  IconButton,
  Slider,
  Typography,
  Divider,
  Stack,
  Tooltip,
} from "@mui/material";
import { useState, useCallback } from "react";
import { useLanguage } from "../../i18n/LanguageContext";

// 注意：以下函数目前未使用，但保留以备将来使用
// 辅助函数：计算合适的间隔（以毫秒为单位）
/*
const calculateInterval = (maxTime: number): number => {
  const targetMarkCount = 10; // 我们希望时间轴上大约有10个主要标记
  const totalMs = maxTime;

  // 计算合适的间隔（毫秒）
  const rawInterval = totalMs / targetMarkCount;

  // 将间隔四舍五入到最接近的"整数"间隔
  const possibleIntervals = [
    500,
    1000, // 1秒
    5000, // 5秒
    10000, // 10秒
    15000, // 15秒
    30000, // 30秒
    60000, // 1分钟
    300000, // 5分钟
    900000, // 15分钟
    1800000, // 30分钟
    600000,
    1800000,
  ];

  // 找到最接近的间隔
  return possibleIntervals.reduce((prev, curr) => {
    return Math.abs(curr - rawInterval) < Math.abs(prev - rawInterval)
      ? curr
      : prev;
  });
};

// 动态生成MARKINGS数组
const generateMarkings = (maxTime: number) => {
  const majorInterval = calculateInterval(maxTime);
  const minorInterval = majorInterval / 5; // 将次要间隔改为主要间隔的1/5

  return [
    {
      interval: majorInterval,
      color: "grey",
      size: 10,
      width: 1,
    },
    {
      interval: minorInterval,
      color: "grey",
      size: 5,
      width: 1,
    },
  ];
};
*/

export const SeekPlayer = observer(() => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const Icon = store.playing ? Pause : PlayArrow;
  const formattedTime = formatTimeToMinSecMili(store.currentTimeInMs);
  const formattedMaxTime = formatTimeToMinSecMili(store.maxTime);
  const [duration, setDuration] = useState(
    store.timelineDisplayDuration / 1000
  );

  // 计算缩放值的辅助函数，将duration从[minDuration, maxDuration]映射到[100, 0]
  const calculateZoomValue = useCallback(
    (durationInSeconds: number) => {
      const maxDuration = store.maxTime / 1000;
      const minDuration = 10;
      // 将duration从[minDuration, maxDuration]映射到[100, 0]
      const zoomValue =
        100 -
        ((durationInSeconds - minDuration) / (maxDuration - minDuration)) * 100;
      return Math.max(0, Math.min(100, zoomValue));
    },
    [store.maxTime]
  );

  // 计算初始缩放值
  const initialZoomValue = calculateZoomValue(
    store.timelineDisplayDuration / 1000
  );

  const [zoomValue, setZoomValue] = useState(initialZoomValue);

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (
        (e.target as HTMLElement).tagName === "INPUT" ||
        (e.target as HTMLElement).tagName === "TEXTAREA"
      )
        return;

      switch (e.key) {
        case " ": // 空格键播放/暂停
          store.setPlaying(!store.playing);
          e.preventDefault();
          break;
        case "Delete": // 删除键删除当前选中元素
          if (store.getActiveElement()) {
            handleDelete();
          }
          break;
        // case "-": // 缩小视图
        //   handleZoomOut();
        //   break;
        // case "=": // 放大视图
        // case "+":
        //   handleZoomIn();
        //   break;
        case "f": // 适应视图
          if (e.shiftKey) {
            // 调用store的方法适应视图
            store.fitTimelineToContent();

            // 手动更新滑块值，确保UI与store状态同步
            const newDuration = store.timelineDisplayDuration / 1000;

            // 更新本地状态
            setDuration(newDuration);
            setZoomValue(calculateZoomValue(newDuration));
          }
          break;
        case "ArrowRight": // 向右移动时间线
          store.handleSeek(store.currentTimeInMs + 5000); // 前进100毫秒

          e.preventDefault();
          break;
        case "ArrowLeft": // 向左移动时间线
          store.handleSeek(Math.max(0, store.currentTimeInMs - 5000)); // 后退100毫秒

          e.preventDefault();
          break;
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [store.playing]);

  // 只在组件挂载时检查timelineDisplayDuration是否已设置
  useEffect(() => {
    // 如果timelineDisplayDuration为0或未设置，则设置一个合理的默认值
    if (store.timelineDisplayDuration <= 0) {
      // 使用10秒作为默认值，与Store构造函数中的设置保持一致
      store.setTimelineDisplayDuration(10000);
    }
    // 不再强制设置为maxTime，避免与Store构造函数中的设置冲突
  }, []);

  // Update local duration state when the store's value changes
  useEffect(() => {
    const newDuration = store.timelineDisplayDuration / 1000;
    setDuration(newDuration);

    // 使用辅助函数计算对应的缩放值
    setZoomValue(calculateZoomValue(newDuration));
  }, [store.timelineDisplayDuration, store.maxTime, calculateZoomValue]);

  const handleDurationChange = (_event: Event, newValue: number | number[]) => {
    // 确保newValue是单个数字（非数组）
    const value = Array.isArray(newValue) ? newValue[0] : newValue;

    // 将滑块值从[0, 100]映射到duration[maxDuration, minDuration]
    const maxDuration = store.maxTime / 1000;
    const minDuration = 10;
    // 反转映射关系：滑块值越大，duration越小
    const newDuration =
      maxDuration - (value / 100) * (maxDuration - minDuration);
    setZoomValue(value);
    setDuration(newDuration);
    store.setTimelineDisplayDuration(newDuration * 1000);
  };

  // 注意：scrollbarInfo 在此组件中未使用，但可能在将来的功能中需要
  // 如果需要在UI中显示滚动条信息，可以取消注释以下代码
  /*
  const scrollbarInfo = useMemo(() => {
    // Calculate the maximum offset allowed using the same logic as in the store
    const maxAllowedTime = Math.max(
      store.timelineDisplayDuration,
      store.maxTime
    );
    const maxOffset = Math.max(
      store.timelineDisplayDuration * 0.5,
      maxAllowedTime - store.timelineDisplayDuration
    );

    // Calculate the total scrollable range (from 0 to maxOffset)
    const totalScrollRange = maxOffset;

    // Calculate the current position as a percentage (0-100)
    // Map from [0, maxOffset] to [0, 100]
    const position = (store.timelinePan.offsetX / totalScrollRange) * 100;

    // Calculate thumb size based on visible range relative to total time
    const visibleRangeRatio = store.timelineDisplayDuration / maxAllowedTime;
    // Make sure thumb isn't too small
    const thumbSize = Math.max(10, visibleRangeRatio * 100);

    return { position, thumbSize };
  }, [store.timelinePan.offsetX, store.timelineDisplayDuration, store.maxTime]);
  */

  // 添加缩放处理函数
  const handleZoomOut = () => {
    const newValue = Math.min(duration * 1.5, store.maxTime / 1000); // 放大1.5倍，最大可以显示整个timeline
    setDuration(newValue);
    store.setTimelineDisplayDuration(newValue * 1000);
  };

  const handleZoomIn = () => {
    const newValue = Math.max(duration / 1.5, 10); // 缩小为原来的2/3，但不小于最小值
    setDuration(newValue);
    store.setTimelineDisplayDuration(newValue * 1000);
  };

  // 处理删除确认
  const handleDelete = () => {
    const activeElement = store.getActiveElement();
    // 检查是否有选中元素
    if (activeElement) {
      store.deleteElement(activeElement.id);
    } else {
      // 如果没有活动元素但有选中元素，则删除选中元素
      if (store.selectedElement) {
        store.deleteElement(store.selectedElement.id);
      }
    }
  };

  const [hasActiveElement, setHasActiveElement] = useState(false);
  const [canSplitElement, setCanSplitElement] = useState(false);

  useEffect(() => {
    setHasActiveElement(!!store.getActiveElement());

    // 检查是否可以分割当前选中的元素
    const selectedElement = store.selectedElement;
    if (selectedElement) {
      const currentTime = store.currentTimeInMs;
      const isInTimeRange =
        currentTime > selectedElement.timeFrame.start &&
        currentTime < selectedElement.timeFrame.end;
      setCanSplitElement(isInTimeRange);
    } else {
      setCanSplitElement(false);
    }
  }, [store.selectedElement, store.currentTimeInMs]);

  // 添加跳到下一个/上一个关键点的功能
  const jumpToPreviousKeypoint = () => {
    // 这里需要实现跳到上一个关键点的逻辑
    // 示例实现：跳到当前时间前的最近关键点或者回到起点
    store.handleSeek(Math.max(0, store.currentTimeInMs - 5 * 1000));
  };

  const jumpToNextKeypoint = () => {
    // 这里需要实现跳到下一个关键点的逻辑
    // 示例实现：跳到当前时间后的最近关键点或者向前移动
    store.handleSeek(Math.min(store.maxTime, store.currentTimeInMs + 5 * 1000));
  };

  // 添加滚轮事件处理函数，防止触发浏览器前进后退
  useEffect(() => {
    // 获取SeekPlayer组件的DOM元素
    const seekPlayerElement = document.querySelector(".seek-player-container");
    if (!seekPlayerElement) return;

    const handleSeekPlayerWheel = (e: WheelEvent) => {
      // 阻止默认行为，防止触发浏览器的前进后退导航
      e.preventDefault();

      // 检测是否按下了Ctrl键（Mac上的Command键）
      const isCtrlPressed = e.ctrlKey || e.metaKey;

      // 如果是Ctrl+滚轮，执行缩放操作
      if (isCtrlPressed) {
        // 根据滚轮方向确定是放大还是缩小
        if (e.deltaY < 0) {
          // 放大时间线（减小显示的时间范围）
          const newValue = Math.max(duration / 1.1, 10); // 缩小为原来的2/3，但不小于最小值
          setDuration(newValue);
          store.setTimelineDisplayDuration(newValue * 1000);
        } else {
          // 缩小时间线（增加显示的时间范围）
          const newValue = Math.min(duration * 1.1, store.maxTime / 1000); // 放大1.5倍，最大可以显示整个timeline
          setDuration(newValue);
          store.setTimelineDisplayDuration(newValue * 1000);
        }
        return;
      }

      // 处理水平滚动
      if (Math.abs(e.deltaX) > Math.abs(e.deltaY) || e.shiftKey) {
        // 使用deltaX或者Shift+deltaY作为水平滚动量
        const delta = e.shiftKey ? e.deltaY : e.deltaX;
        // 调用store的滚动处理方法
        store.handleTimelineWheel(delta);
      }
    };

    // 添加非被动的wheel事件监听器
    seekPlayerElement.addEventListener("wheel", handleSeekPlayerWheel, {
      passive: false,
    });

    return () => {
      seekPlayerElement.removeEventListener("wheel", handleSeekPlayerWheel);
    };
  }, [store, duration]);

  return (
    <Box
      className="seek-player-container"
      sx={{
        display: "flex",
        flexDirection: "column",
        width: "100%",
        mx: 1,
        p: 0,
      }}
    >
      <Divider sx={{ borderColor: "divider" }} />
      <Stack
        direction="row"
        alignItems="center"
        spacing={2}
        sx={{
          p: 0.5,
        }}
      >
        <Tooltip title={t("seekplayer_delete")}>
          <span>
            <IconButton
              size="small"
              sx={{
                color: "error.light",
                transition: "all 0.2s",
                "&:hover": {
                  color: "error.main",
                  transform: "scale(1.1)",
                },
              }}
              onClick={handleDelete}
              disabled={!hasActiveElement && !store.selectedElement}
            >
              <Delete fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>

        <Tooltip title={t("seekplayer_split_element")}>
          <span>
            <IconButton
              size="small"
              sx={{
                color: "primary.light",
                transition: "all 0.2s",
                "&:hover": {
                  color: "primary.main",
                  transform: "scale(1.1)",
                },
              }}
              onClick={() => {
                if (store.selectedElement && canSplitElement) {
                  store.splitElement(
                    store.selectedElement.id,
                    store.currentTimeInMs
                  );
                }
              }}
              disabled={!canSplitElement}
            >
              <ContentCut fontSize="small" />
            </IconButton>
          </span>
        </Tooltip>

        <Box
          sx={{
            flex: 1,
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            gap: 1,
            userSelect: "none",
          }}
        >
          <Tooltip title={t("seekplayer_seek_backward")}>
            <span>
              <IconButton
                onClick={jumpToPreviousKeypoint}
                size="small"
                sx={{
                  color: "primary.main",
                  "&:hover": {
                    bgcolor: "primary.lighter",
                    transform: "scale(1.1)",
                  },
                  transition: "all 0.2s",
                }}
                disabled={store.editorElements.length === 0} // 禁用按钮，如果没有元素
              >
                <SkipPrevious fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>

          <Tooltip
            title={
              store.playing
                ? `${t("seekplayer_pause")} (Space)`
                : `${t("seekplayer_play")} (Space)`
            }
          >
            <span>
              <IconButton
                onClick={() => {
                  store.setPlaying(!store.playing);
                }}
                sx={{
                  color: "primary.main",
                  "&:hover": {
                    bgcolor: "primary.lighter",
                    transform: "scale(1.1)",
                  },
                  transition: "all 0.2s",
                }}
                aria-label={
                  store.playing ? t("seekplayer_pause") : t("seekplayer_play")
                }
                disabled={store.editorElements.length === 0} // 禁用播放按钮，如果没有元素
              >
                <Icon />
              </IconButton>
            </span>
          </Tooltip>

          <Tooltip title={t("seekplayer_seek_forward")}>
            <span>
              <IconButton
                onClick={jumpToNextKeypoint}
                size="small"
                sx={{
                  color: "primary.main",
                  "&:hover": {
                    bgcolor: "primary.lighter",
                    transform: "scale(1.1)",
                  },
                  transition: "all 0.2s",
                }}
                disabled={store.editorElements.length === 0} // 禁用按钮，如果没有元素
              >
                <SkipNext fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>

          <Typography variant="subtitle2" color="text.secondary">
            {formattedTime}
          </Typography>
          <Divider orientation="vertical" sx={{ height: 20 }} />
          <Typography variant="subtitle2" color="text.secondary">
            {formattedMaxTime}
          </Typography>
        </Box>

        <Box
          sx={{
            width: "200px",
            display: "flex",
            alignItems: "center",
            gap: 1,
          }}
        >
          <Tooltip title={`${t("seekplayer_zoom_out")} (-)`}>
            <IconButton
              onClick={handleZoomOut}
              size="small"
              sx={{
                color: "primary.main",
                transition: "all 0.2s",
                "&:hover": {
                  transform: "scale(1.1)",
                },
              }}
            >
              <ZoomOut />
            </IconButton>
          </Tooltip>
          <Slider
            size="small"
            min={0}
            max={100}
            value={zoomValue}
            onChange={handleDurationChange}
            sx={{
              color: "primary.main",
            }}
          />
          <Tooltip title={`${t("seekplayer_zoom_in")} (+)`}>
            <IconButton
              onClick={handleZoomIn}
              size="small"
              sx={{
                color: "primary.main",
                transition: "all 0.2s",
                "&:hover": {
                  transform: "scale(1.1)",
                },
              }}
            >
              <ZoomIn />
            </IconButton>
          </Tooltip>
          <Tooltip title={`${t("seekplayer_fit_view")} (F)`}>
            <IconButton
              onClick={() => {
                // 调用store的方法适应视图
                store.fitTimelineToContent();

                // 手动更新滑块值，确保UI与store状态同步
                const newDuration = store.timelineDisplayDuration / 1000;

                // 更新本地状态
                setDuration(newDuration);
                setZoomValue(calculateZoomValue(newDuration));
              }}
              size="small"
              sx={{
                color: "primary.main",
                transition: "all 0.2s",
                "&:hover": {
                  transform: "scale(1.1)",
                },
              }}
            >
              <CenterFocusStrong />
            </IconButton>
          </Tooltip>
        </Box>
      </Stack>
    </Box>
  );
});

import { Box, Typography, IconButton, Grid } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import BlockIcon from "@mui/icons-material/Block";
import { useState } from "react";
import React from "react";
import fabric from "fabric";
import { StoreContext } from "../../store";
import { EditorElement, TextEditorElement } from "../../types";

interface PresetsProps {
  element: EditorElement | null;
}

const Presets = ({ element }: PresetsProps) => {
  const [selectedPreset, setSelectedPreset] = useState(null);
  const store = React.useContext(StoreContext);

  // 添加空值检查和类型检查
  if (!element || !element.properties) {
    return <></>;
  }

  // 现在我们知道 element 是 TextEditorElement 类型
  const textElement = element as TextEditorElement;

  const presets = [
    { id: 1, icon: <BlockIcon sx={{ color: "#666666" }} /> },
    {
      id: 2,
      text: "Text",
      backgroundColor: "#000000",
      color: "#ffffff",
      style: {
        WebkitTextStroke: "1px #ffffff",
        textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
      },
    },
    {
      id: 3,
      text: "Text",
      backgroundColor: "transparent",
      color: "#000000",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 4,
      text: "Text",
      backgroundColor: "#ffffff",
      color: "#000000",
      style: {
        textShadow: "2px 2px 2px rgba(0,0,0)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 5,
      text: "Text",
      backgroundColor: "transparent",
      color: "#000000",
      style: {
        textShadow: "2px 2px 4px rgba(0,0,0)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 6,
      text: "Text",
      backgroundColor: "#6200ee",
      color: "#ffffff",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 7,
      text: "Text",
      backgroundColor: "#ffd700",
      color: "#000000",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 8,
      text: "Text",
      backgroundColor: "transparent",
      color: "#0066ff",
      style: {
        textShadow: "2px 2px 4px rgba(0,102,255)",
        WebkitTextStroke: "0px #000000",
      },
    },
    {
      id: 9,
      text: "Text",
      backgroundColor: "transparent",
      color: "#000000",
      style: {
        WebkitTextStroke: "1px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 10,
      text: "Text",
      backgroundColor: "#000000",
      color: "#00ff9d",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 11,
      text: "Text",
      backgroundColor: "transparent",
      color: "#ff69b4",
      style: {
        textShadow: "2px 2px 4px rgba(255,105,180,0.3)",
        WebkitTextStroke: "0px rgba(0,0,0)",
      },
    },
    {
      id: 12,
      text: "Text",
      backgroundColor: "transparent",
      color: "#008000",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },

    { id: 14, text: "Text", backgroundColor: "#000000", color: "#ffd700" },
    {
      id: 15,
      text: "Text",
      backgroundColor: "transparent",
      color: "#87ceeb",
      style: {
        WebkitTextStroke: "0px rgba(0,0,0)",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
    {
      id: 16,
      text: "Text",
      backgroundColor: "transparent",
      color: "#ffffff",
      style: {
        WebkitTextStroke: "1px #ff0000",
        textShadow: "0px 0px 0px rgba(0,0,0)",
      },
    },
  ];

  const handlePresetClick = (presetId) => {
    setSelectedPreset(presetId);

    // Get the selected preset
    const preset = presets.find((preset) => preset.id === presetId);
    if (
      !preset ||
      !store.selectedElement ||
      !store.selectedElement.fabricObject
    )
      return;

    const styleUpdate: any = {
      backgroundColor: preset.backgroundColor,
      fontColor: preset.color,
    };

    // Add shadow settings if the preset has textShadow
    if (preset.style?.textShadow) {
      // Parse the text shadow values
      const shadowMatch = preset.style.textShadow.match(
        /([0-9]+px) ([0-9]+px) ([0-9]+px) (rgba?\([^)]+\))/
      );
      if (shadowMatch) {
        styleUpdate.shadowOffsetX = parseInt(shadowMatch[1]);
        styleUpdate.shadowOffsetY = parseInt(shadowMatch[2]);
        styleUpdate.shadowBlur = parseInt(shadowMatch[3]);
        styleUpdate.shadowColor = shadowMatch[4];
      }
    }

    if (preset.style?.WebkitTextStroke) {
      // Parse the border values (e.g., "2px solid #000000")
      const borderMatch = preset.style.WebkitTextStroke.match(
        /([0-9]+px) (#[0-9a-f]+)/i
      );
      if (borderMatch) {
        styleUpdate.strokeWidth = parseInt(borderMatch[1]);
        styleUpdate.strokeColor = borderMatch[2];
      }
    }
    console.log("WebkitTextStroke", styleUpdate);
    store.updateTextStyle(textElement.id, styleUpdate);
  };

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderBottom: "1px solid rgba(0, 0, 0, 0.08)",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
          Presets
        </Typography>
      </Box>
      <Box
        sx={{
          height: "100%",
          overflow: "auto",
          "&::-webkit-scrollbar": {
            width: "6px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "3px",
          },
        }}
      >
        <Grid container spacing={2} sx={{ p: 2 }}>
          {presets.map((preset) => (
            <Grid item xs={4} key={preset.id}>
              <Box
                onClick={() => handlePresetClick(preset.id)}
                sx={{
                  aspectRatio: "1",
                  backgroundColor: "white",
                  borderRadius: "8px",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  p: 1.5,
                  border:
                    selectedPreset === preset.id
                      ? "2px solid #1976d2"
                      : "1px solid rgba(0, 0, 0, 0.12)",
                  cursor: "pointer",
                  transition: "all 0.2s ease-in-out",
                  "&:hover": {
                    transform: "scale(1.02)",
                    boxShadow: "0 2px 12px rgba(0,0,0,0.08)",
                  },
                  position: "relative",
                }}
              >
                {preset.id === 1 ? (
                  <BlockIcon sx={{ color: "#666", fontSize: 24 }} />
                ) : (
                  <Typography
                    sx={{
                      fontSize: "1rem",
                      fontWeight: 600,
                      padding: "3px 8px",
                      borderRadius: "4px",
                      backgroundColor: preset.backgroundColor,
                      color: preset.color,
                      ...preset.style,
                    }}
                  >
                    {preset.text}
                  </Typography>
                )}
              </Box>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default Presets;

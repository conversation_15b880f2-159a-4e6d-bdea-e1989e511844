import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  Typo<PERSON>,
  Slider,
  Grid,
  Divider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  IconButton,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../../store";
import { ShapeEditorElement, ShapeType, EditorElement } from "../../types";
import BaseSetting from "./BaseSetting";
import { useLanguage } from "../../i18n/LanguageContext";
import { UnifiedColorPicker } from "../../components/common/UnifiedColorPicker";
import FormatColorFillIcon from "@mui/icons-material/FormatColorFill";
import BorderStyleIcon from "@mui/icons-material/BorderStyle";
import PaletteIcon from "@mui/icons-material/Palette";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import ShapeLineIcon from "@mui/icons-material/ShapeLine";

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,
  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

interface BasicShapeProps {
  element: EditorElement | null;
}

const BasicShape = observer(({ element }: BasicShapeProps) => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();

  // 状态
  const [fill, setFill] = useState("#9e9e9e");
  const [stroke, setStroke] = useState("#757575");
  const [strokeWidth, setStrokeWidth] = useState(1);
  const [borderRadius, setBorderRadius] = useState(0);
  const [backgroundColor, setBackgroundColor] = useState("transparent");
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    if (element?.type === "shape" && element?.properties) {
      const shapeElement = element as ShapeEditorElement;
      setFill(shapeElement.properties.fill || "#9e9e9e");
      setStroke(shapeElement.properties.stroke || "#757575");
      setStrokeWidth(shapeElement.properties.strokeWidth || 1);
      setBorderRadius(shapeElement.properties.borderRadius || 0);
      setBackgroundColor(
        shapeElement.properties.backgroundColor || "transparent"
      );
    }
  }, [element]);

  // 添加空值检查和类型检查
  if (!element || !element.properties) {
    return <></>;
  }

  // 检查是否为形状元素
  if (element.type !== "shape") {
    return <></>;
  }

  // 现在我们知道 element 是 ShapeEditorElement 类型
  const shapeElement = element as ShapeEditorElement;

  const handleFillChange = (color: string) => {
    setFill(color);
    updateShapeProperties({ fill: color });
  };

  const handleStrokeChange = (color: string) => {
    setStroke(color);
    updateShapeProperties({ stroke: color });
  };

  const handleBackgroundColorChange = (color: string) => {
    setBackgroundColor(color);
    updateShapeProperties({ backgroundColor: color });
  };

  const handleStrokeWidthChange = (event: Event, value: number | number[]) => {
    const newValue = value as number;
    setStrokeWidth(newValue);
    updateShapeProperties({ strokeWidth: newValue });
  };

  const handleBorderRadiusChange = (event: Event, value: number | number[]) => {
    const newValue = value as number;
    setBorderRadius(newValue);
    updateShapeProperties({ borderRadius: newValue });
  };

  const handleShapeTypeChange = (
    event: React.ChangeEvent<{ value: unknown }>
  ) => {
    const newShapeType = event.target.value as ShapeType;
    updateShapeProperties({ shapeType: newShapeType });
  };

  const updateShapeProperties = (
    properties: Partial<ShapeEditorElement["properties"]>
  ) => {
    if (!shapeElement) return;

    const updatedElement = {
      ...shapeElement,
      properties: {
        ...shapeElement.properties,
        ...properties,
      },
    };

    store.updateEditorElement(updatedElement);
    store.refreshElements();
    store.updateMediaElements();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleResetProperties = () => {
    const defaultProperties = {
      fill: "#9e9e9e",
      stroke: "#757575",
      strokeWidth: 1,
      backgroundColor: "transparent",
    };

    setFill(defaultProperties.fill);
    setStroke(defaultProperties.stroke);
    setStrokeWidth(defaultProperties.strokeWidth);
    setBackgroundColor(defaultProperties.backgroundColor);

    updateShapeProperties(defaultProperties);
  };

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          {t("shape_properties")}
        </Typography>
      </Box>
      <Divider />
      <Box
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",
          pr: 2,
          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            borderRadius: "2px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          },
        }}
      >
        <StyledTabs value={activeTab} onChange={handleTabChange}>
          <StyledTab label={t("basic")} />
          <StyledTab label={t("advanced")} />
        </StyledTabs>

        {activeTab === 0 && (
          <Box>
            <BaseSetting element={shapeElement} />

            {/* 控制按钮 */}
            <Stack direction="row" spacing={2} sx={{ mb: 2, mt: 1 }}>
              <Tooltip title={t("reset_filters")}>
                <IconButton
                  onClick={handleResetProperties}
                  color="primary"
                  size="small"
                >
                  <RestartAltIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Stack>

            {/* 形状类型选择 */}
            <Stack spacing={2} sx={{ mb: 3 }}>
              <Stack
                direction="row"
                spacing={1}
                alignItems="center"
                sx={{ mb: 1 }}
              >
                <ShapeLineIcon fontSize="small" color="primary" />
                <Typography variant="subtitle2" sx={{ fontWeight: "medium" }}>
                  {t("shape_type")}
                </Typography>
              </Stack>
              <FormControl fullWidth size="small">
                <Select
                  value={shapeElement.properties.shapeType}
                  onChange={handleShapeTypeChange as any}
                  displayEmpty
                >
                  <MenuItem value="rect">{t("rectangle")}</MenuItem>
                  <MenuItem value="roundedRect">
                    {t("rounded_rectangle")}
                  </MenuItem>
                  <MenuItem value="circle">{t("circle")}</MenuItem>
                  <MenuItem value="ellipse">{t("ellipse")}</MenuItem>
                  <MenuItem value="triangle">{t("triangle")}</MenuItem>
                  <MenuItem value="line">{t("line")}</MenuItem>
                  <MenuItem value="pentagon">{t("pentagon")}</MenuItem>
                  <MenuItem value="hexagon">{t("hexagon")}</MenuItem>
                  <MenuItem value="octagon">{t("octagon")}</MenuItem>
                  <MenuItem value="parallelogram">
                    {t("parallelogram")}
                  </MenuItem>
                  <MenuItem value="arch">{t("arch")}</MenuItem>
                </Select>
              </FormControl>
            </Stack>
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            {/* 颜色设置 */}
            <Stack spacing={3}>
              {/* 背景颜色 */}
              <Box>
                <Stack
                  direction="row"
                  spacing={1}
                  alignItems="center"
                  sx={{ mb: 1 }}
                >
                  <PaletteIcon fontSize="small" color="primary" />
                  <Typography variant="subtitle2" sx={{ fontWeight: "medium" }}>
                    {t("background_color")}
                  </Typography>
                </Stack>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <UnifiedColorPicker
                    color={backgroundColor}
                    onChange={handleBackgroundColorChange}
                    allowTransparent={true}
                  />
                </Box>
              </Box>

              {/* 填充颜色 */}
              <Box>
                <Stack
                  direction="row"
                  spacing={1}
                  alignItems="center"
                  sx={{ mb: 1 }}
                >
                  <FormatColorFillIcon fontSize="small" color="primary" />
                  <Typography variant="subtitle2" sx={{ fontWeight: "medium" }}>
                    {t("fill_color")}
                  </Typography>
                </Stack>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mb: 1,
                  }}
                >
                  <UnifiedColorPicker
                    color={fill}
                    onChange={handleFillChange}
                  />
                </Box>
              </Box>

              {/* 边框设置 */}
              <Box>
                <Stack
                  direction="row"
                  spacing={1}
                  alignItems="center"
                  sx={{ mb: 1 }}
                >
                  <BorderStyleIcon fontSize="small" color="primary" />
                  <Typography variant="subtitle2" sx={{ fontWeight: "medium" }}>
                    {t("border_settings")}
                  </Typography>
                </Stack>

                {/* 边框颜色 */}
                <Typography variant="body2" gutterBottom>
                  {t("border_color")}
                </Typography>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    mb: 2,
                  }}
                >
                  <UnifiedColorPicker
                    color={stroke}
                    onChange={handleStrokeChange}
                  />
                </Box>

                {/* 边框宽度 */}
                <Typography variant="body2" gutterBottom>
                  {t("border_width")}: {strokeWidth}px
                </Typography>
                <Slider
                  value={strokeWidth}
                  onChange={handleStrokeWidthChange}
                  min={0}
                  max={20}
                  step={1}
                  sx={{ mb: 2 }}
                />

                {/* 边框圆角 - 仅对矩形和圆角矩形显示 */}
                {(shapeElement.properties.shapeType === "rect" ||
                  shapeElement.properties.shapeType === "roundedRect") && (
                  <>
                    <Typography variant="body2" gutterBottom>
                      {t("border_radius")}: {borderRadius}px
                    </Typography>
                    <Slider
                      value={borderRadius}
                      onChange={handleBorderRadiusChange}
                      min={0}
                      max={50}
                      step={1}
                    />
                  </>
                )}
              </Box>
            </Stack>
          </Box>
        )}
      </Box>
    </Box>
  );
});

export default BasicShape;

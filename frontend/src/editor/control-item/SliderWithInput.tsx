import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON>lider,
  <PERSON><PERSON>ield,
  <PERSON>ack,
  Typography,
  Tooltip,
  alpha,
} from "@mui/material";

interface SliderWithInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  onChangeCommitted?: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  width?: string;
  textFieldWidth?: string;
  unit?: string;
}

const SliderWithInput = ({
  label,
  value,
  onChange,
  onChangeCommitted,
  min,
  max,
  step = 1,
  textFieldWidth = "60px",
  unit,
}: SliderWithInputProps) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [inputValue, setInputValue] = useState<string>(
    step < 1 ? value.toFixed(2) : value.toString()
  );
  const [isInputFocused, setIsInputFocused] = useState(false);

  // 同步外部 value 变化到 inputValue，但只在用户没有正在输入时同步
  useEffect(() => {
    if (!isInputFocused) {
      setInputValue(step < 1 ? value.toFixed(2) : value.toString());
    }
  }, [value, step, isInputFocused]);

  const handleSliderChange = (e: any, newValue: number | number[]) => {
    const numValue = Number(newValue);
    onChange(numValue);
    setInputValue(step < 1 ? numValue.toFixed(2) : numValue.toString());
  };

  return (
    <Stack spacing={0.5}>
      <Typography
        variant="body2"
        sx={{
          color: "text.secondary",
          fontWeight: 500,
        }}
      >
        {label}
      </Typography>

      <Stack direction="row" spacing={2} alignItems="center" sx={{ pl: 1 }}>
        <Box sx={{ flexGrow: 1 }}>
          <Tooltip
            open={showTooltip}
            title={`${value}${unit || ""}`}
            arrow
            placement="top"
          >
            <Slider
              size="small"
              value={value}
              onChange={handleSliderChange}
              onMouseEnter={() => setShowTooltip(true)}
              onMouseLeave={() => setShowTooltip(false)}
              onChangeCommitted={
                onChangeCommitted
                  ? (e, newValue) => onChangeCommitted(Number(newValue))
                  : undefined
              }
              min={min}
              max={max}
              step={step}
            />
          </Tooltip>
        </Box>

        <Box
          sx={{
            backgroundColor: "grey.100",
            borderRadius: 1,
            px: 1,
            py: 0.3,
            minWidth: "50px",
            textAlign: "center",
            border: "1px solid #e0e0e0",
          }}
        >
          <Typography
            variant="caption"
            sx={{
              fontWeight: 400,
              color: "text.primary",
            }}
          >
            {step < 1 ? value.toFixed(2) : value.toString()}
            {unit || ""}
          </Typography>
        </Box>
      </Stack>
    </Stack>
  );
};

export default SliderWithInput;

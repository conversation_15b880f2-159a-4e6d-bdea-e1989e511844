import React from "react";
import SliderWithInput from "./SliderWithInput";
import { Stack, Typography } from "@mui/material";
import { useLanguage } from "../../i18n/LanguageContext";

const VolumeControl = ({ element }) => {
  const { t } = useLanguage();
  const [volume, setVolume] = React.useState(element.volume || 1);

  const handleVolumeChange = (newValue) => {
    const volume = Array.isArray(newValue) ? newValue[0] : newValue;
    setVolume(volume);
  };

  const handleVolumeChangeCommitted = (newValue) => {
    const volume = Array.isArray(newValue) ? newValue[0] : newValue;
    if (element) {
      const mediaElement = document.getElementById(
        element.properties.elementId
      ) as HTMLVideoElement;
      if (mediaElement) {
        mediaElement.volume = volume;
        element.volume = volume;
      }
    }
  };

  return (
    <Stack sx={{ m: 1 }}>
      <SliderWithInput
        label={t("volume")}
        value={volume}
        min={0}
        max={1}
        step={0.1}
        onChange={(newValue) => handleVolumeChange(Number(newValue))}
        onChangeCommitted={(newValue) =>
          handleVolumeChangeCommitted(Number(newValue))
        }
      />
    </Stack>
  );
};

export default VolumeControl;

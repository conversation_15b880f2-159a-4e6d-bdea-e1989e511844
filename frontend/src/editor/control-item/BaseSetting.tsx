import React, { useCallback, useContext, useState, useEffect } from "react";
import { observer } from "mobx-react";
import debounce from "lodash.debounce";

// Material-UI Icons
import AlignHorizontalCenterIcon from "@mui/icons-material/AlignHorizontalCenter";
import AlignHorizontalLeftIcon from "@mui/icons-material/AlignHorizontalLeft";
import AlignHorizontalRightIcon from "@mui/icons-material/AlignHorizontalRight";
import AlignVerticalBottomIcon from "@mui/icons-material/AlignVerticalBottom";
import AlignVerticalCenterIcon from "@mui/icons-material/AlignVerticalCenter";
import AlignVerticalTopIcon from "@mui/icons-material/AlignVerticalTop";
import DeleteIcon from "@mui/icons-material/Delete";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import FlipIcon from "@mui/icons-material/Flip";
import FormatAlignJustifyIcon from "@mui/icons-material/FormatAlignJustify";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import LockIcon from "@mui/icons-material/Lock";
import LockOpenIcon from "@mui/icons-material/LockOpen";
import Opacity from "@mui/icons-material/Opacity";

// Material-UI Components
import {
  Box,
  Grid,
  IconButton,
  Popover,
  Slider,
  Stack,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";

// Local imports
import { StoreContext } from "../../store";
import { EditorElement } from "../../types";
import { useLanguage } from "../../i18n/LanguageContext";

// Constants
const OPACITY_SLIDER_CONFIG = {
  step: 1,
  min: 0,
  max: 100,
  color: "#1976d2",
  thumbSize: 12,
};

const POPOVER_WIDTH = 250;

const DEBOUNCE_WAIT = 500;

const ALIGNMENT_BUTTONS = [
  {
    icon: AlignHorizontalLeftIcon,
    align: "left",
    tooltipKey: "align_left",
  },
  {
    icon: AlignHorizontalCenterIcon,
    align: "center",
    tooltipKey: "align_center",
  },
  {
    icon: AlignHorizontalRightIcon,
    align: "right",
    tooltipKey: "align_right",
  },
  {
    icon: FormatAlignJustifyIcon,
    align: "justify",
    tooltipKey: "justify",
  },
  {
    icon: AlignVerticalTopIcon,
    align: "top",
    tooltipKey: "align_top",
  },
  {
    icon: AlignVerticalCenterIcon,
    align: "middle",
    tooltipKey: "align_middle",
  },
  {
    icon: AlignVerticalBottomIcon,
    align: "bottom",
    tooltipKey: "align_bottom",
  },
] as const;

const SLIDER_STYLES = {
  color: OPACITY_SLIDER_CONFIG.color,
  "& .MuiSlider-thumb": {
    width: OPACITY_SLIDER_CONFIG.thumbSize,
    height: OPACITY_SLIDER_CONFIG.thumbSize,
    transition: "0.3s cubic-bezier(.47,1.64,.41,.8)",
    "&:before": {
      boxShadow: "0 2px 12px 0 rgba(0,0,0,0.4)",
    },
    "&:hover, &.Mui-focusVisible": {
      boxShadow: `0px 0px 0px 8px rgb(25 118 210 / 16%)`,
    },
  },
  "& .MuiSlider-rail": {
    opacity: 0.5,
  },
};

// Types
interface BaseSettingProps {
  element: EditorElement | null;
}

type FlipType = "horizontal" | "vertical";
type LayoutProperty = "x" | "y" | "width" | "height" | "rotation";

const BaseSetting = ({ element }: BaseSettingProps) => {
  const store = useContext(StoreContext);
  const { t } = useLanguage();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [opacity, setOpacity] = useState(element?.opacity ?? 1);
  const [placement, setPlacement] = useState(element?.placement);

  useEffect(() => {
    if (element) {
      setOpacity(element.opacity);
      setPlacement(element.placement);
    }
  }, [element]);

  const isOpacityPopoverOpen = Boolean(anchorEl);

  // Event handlers with useCallback for performance
  const handleAlign = useCallback(
    (alignType: string) => {
      if (!element) return;
      store.alignElement(element.id, alignType);
    },
    [element, store]
  );

  const handleLock = useCallback(() => {
    if (!element) return;
    store.toggleLockElement(element.id);
  }, [element, store]);

  const handleClone = useCallback(() => {
    if (!element) return;
    store.cloneElement(element.id);
  }, [element, store]);

  const handleDelete = useCallback(() => {
    if (!element) return;
    store.deleteElement(element.id);
  }, [element, store]);

  const handleFullscreen = useCallback(() => {
    if (!element) return;
    store.setElementFullscreen(element.id);
  }, [element, store]);

  const handleOpacityClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleOpacityClose = () => {
    setAnchorEl(null);
  };

  const handleFlip = useCallback(
    (flipType: FlipType) => {
      if (!element) return;
      store.flipElement(element.id, flipType);
    },
    [element, store]
  );

  const updateStoreLayout = useCallback(
    debounce((newPlacement) => {
      if (!element?.fabricObject || !newPlacement) return;

      element.fabricObject.set({
        left: newPlacement.x,
        top: newPlacement.y,
        width: newPlacement.width,
        height: newPlacement.height,
        angle: newPlacement.rotation,
      });

      if (element.type === "text") {
        element.fabricObject.set({
          scaleX: 1,
          scaleY: 1,
        });
      }

      const updatedElement = {
        ...element,
        placement: newPlacement,
      };

      store.updateEditorElement(updatedElement);
      store.canvas.requestRenderAll();
    }, DEBOUNCE_WAIT),
    [element, store]
  );

  const updateStoreOpacity = useCallback(
    debounce((newOpacity: number) => {
      if (element) {
        store.updateElementOpacity(element.id, newOpacity);
      }
    }, DEBOUNCE_WAIT),
    [element, store]
  );

  const handleOpacityChange = (_: Event, newValue: number | number[]) => {
    const newOpacity = (newValue as number) / 100;
    setOpacity(newOpacity);
    updateStoreOpacity(newOpacity);
  };

  const handleLayoutChange = (property: LayoutProperty, value: number) => {
    if (!placement) return;
    const newPlacement = { ...placement, [property]: value };
    setPlacement(newPlacement);
    updateStoreLayout(newPlacement);
  };

  // Early return for no element selected
  if (!element) {
    return (
      <Box sx={{ height: "100%", p: 2 }}>
        <Typography variant="body2" color="text.secondary">
          {t("no_element_selected")}
        </Typography>
      </Box>
    );
  }

  // Helper function to create layout input
  const createLayoutInput = (
    label: string,
    property: LayoutProperty,
    value: number | undefined
  ) => (
    <TextField
      label={label}
      type="number"
      value={Math.round(value || 0)}
      onChange={(e) => handleLayoutChange(property, Number(e.target.value))}
      fullWidth
      size="small"
    />
  );

  return (
    <Box sx={{ height: "100%" }}>
      {/* Action Buttons */}
      <Stack direction="row" justifyContent="space-between" sx={{ mb: 2 }}>
        <Tooltip title={element.locked ? t("unlock") : t("lock")}>
          <IconButton onClick={handleLock}>
            {element.locked ? (
              <LockIcon fontSize="small" />
            ) : (
              <LockOpenIcon fontSize="small" />
            )}
          </IconButton>
        </Tooltip>

        <Tooltip title={t("opacity")}>
          <IconButton onClick={handleOpacityClick}>
            <Opacity fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("clone")}>
          <IconButton onClick={handleClone}>
            <FileCopyIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("delete")}>
          <IconButton onClick={handleDelete}>
            <DeleteIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("fullscreen")}>
          <IconButton onClick={handleFullscreen}>
            <FullscreenIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("flip_horizontal")}>
          <IconButton onClick={() => handleFlip("horizontal")}>
            <FlipIcon fontSize="small" />
          </IconButton>
        </Tooltip>

        <Tooltip title={t("flip_vertical")}>
          <IconButton onClick={() => handleFlip("vertical")}>
            <FlipIcon fontSize="small" sx={{ transform: "rotate(90deg)" }} />
          </IconButton>
        </Tooltip>
      </Stack>

      {/* Opacity Popover */}
      <Popover
        open={isOpacityPopoverOpen}
        anchorEl={anchorEl}
        onClose={handleOpacityClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "center",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "center",
        }}
      >
        <Box sx={{ p: 2, width: POPOVER_WIDTH }}>
          <Stack direction="row" spacing={2} alignItems="center">
            <Slider
              value={opacity * 100}
              onChange={handleOpacityChange}
              aria-labelledby="opacity-slider"
              valueLabelDisplay="off"
              step={OPACITY_SLIDER_CONFIG.step}
              min={OPACITY_SLIDER_CONFIG.min}
              max={OPACITY_SLIDER_CONFIG.max}
              sx={SLIDER_STYLES}
            />
            <Typography
              variant="body2"
              sx={{ minWidth: 35, textAlign: "right", color: "text.secondary" }}
            >
              {Math.round(opacity * 100)}%
            </Typography>
          </Stack>
        </Box>
      </Popover>

      {/* Alignment Section */}
      <Typography variant="subtitle2" sx={{ mb: 1, color: "text.secondary" }}>
        {t("alignment")}
      </Typography>

      <Stack direction="row">
        {ALIGNMENT_BUTTONS.map(({ icon: Icon, align, tooltipKey }, index) => (
          <Tooltip key={index} title={t(tooltipKey)}>
            <IconButton onClick={() => handleAlign(align)}>
              <Icon fontSize="small" />
            </IconButton>
          </Tooltip>
        ))}
      </Stack>

      {/* Position Section */}
      <Typography variant="subtitle2" sx={{ my: 1, color: "text.secondary" }}>
        {t("position")}
      </Typography>

      <Grid container spacing={2} sx={{ mb: 2 }}>
        <Grid item xs={6}>
          {createLayoutInput(t("position_x"), "x", placement?.x)}
        </Grid>
        <Grid item xs={6}>
          {createLayoutInput(t("position_y"), "y", placement?.y)}
        </Grid>
        <Grid item xs={6}>
          {createLayoutInput(t("width"), "width", placement?.width)}
        </Grid>
        <Grid item xs={6}>
          {createLayoutInput(t("height"), "height", placement?.height)}
        </Grid>
        <Grid item xs={12}>
          {createLayoutInput(t("rotation"), "rotation", placement?.rotation)}
        </Grid>
      </Grid>
    </Box>
  );
};

export default BaseSetting;

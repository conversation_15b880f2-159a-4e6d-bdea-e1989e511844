import { createTheme, ThemeOptions } from "@mui/material/styles";

// Define common theme configurations for both light and dark themes
const commonThemeSettings: ThemeOptions = {};

export const lightTheme = createTheme({
  ...commonThemeSettings,
  palette: {
    mode: "light",
    primary: {
      main: "#4361ee", // Modern vibrant blue
      light: "#738efd",
      dark: "#2f49cc",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#9c27b0", // Rich purple
      light: "#bb6bc9",
      dark: "#7b1fa2",
    },
    grey: {
      100: "#f3f4f6", // Light grey for backgrounds
      200: "#e5e7eb",
      300: "#d1d5db",
      400: "#9ca3af",
      500: "#6b7280",
      600: "#4b5563",
      700: "#374151",
      800: "#1f2937",
      900: "#111827",
    },
    background: {
      default: "#f8fafc", // Light blueish background
      paper: "#ffffff",
    },
    success: {
      main: "#10b981", // Fresh green
      light: "#34d399",
      dark: "#059669",
    },
    error: {
      main: "#ef4444", // Vibrant red
      light: "#f87171",
      dark: "#dc2626",
    },
    warning: {
      main: "#f59e0b", // Warm amber
      light: "#fbbf24",
      dark: "#d97706",
    },
    info: {
      main: "#3b82f6", // Bright blue
      light: "#60a5fa",
      dark: "#2563eb",
    },
    text: {
      primary: "#1e293b", // Dark slate
      secondary: "#64748b", // Medium slate
    },
    divider: "rgba(0, 0, 0, 0.08)",
  },
});

export const darkTheme = createTheme({
  ...commonThemeSettings,
  palette: {
    mode: "dark",
    primary: {
      main: "#60a5fa", // Bright blue for dark mode
      light: "#93c5fd",
      dark: "#3b82f6",
      contrastText: "#ffffff",
    },
    secondary: {
      main: "#c084fc", // Lavender purple for dark mode
      light: "#d8b4fe",
      dark: "#a855f7",
    },
    grey: {
      100: "#1f2937", // Darker grey for dark mode backgrounds
      200: "#374151",
      300: "#4b5563",
      400: "#6b7280",
      500: "#9ca3af",
      600: "#d1d5db",
      700: "#e5e7eb",
      800: "#f3f4f6",
      900: "#f9fafb",
    },
    background: {
      default: "#0f172a", // Deep navy background
      paper: "#1e293b", // Lighter slate for cards/panels
    },
    success: {
      main: "#34d399", // Bright teal
      light: "#6ee7b7",
      dark: "#10b981",
    },
    error: {
      main: "#f87171", // Soft red
      light: "#fca5a5",
      dark: "#ef4444",
    },
    warning: {
      main: "#fbbf24", // Glowing amber
      light: "#fcd34d",
      dark: "#f59e0b",
    },
    info: {
      main: "#60a5fa", // Sky blue
      light: "#93c5fd",
      dark: "#3b82f6",
    },
    text: {
      primary: "#f1f5f9", // Very light slate
      secondary: "#cbd5e1", // Light slate
    },
    divider: "rgba(255, 255, 255, 0.08)",
  },
});

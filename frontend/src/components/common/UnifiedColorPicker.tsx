import React, { useState, useCallback, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>r,
  IconButton,
  Button,
  Toolt<PERSON>,
  <PERSON>rid,
  <PERSON><PERSON>,
  Tab,
} from "@mui/material";
import { SketchPicker, ChromePicker, ColorResult } from "react-color";
import FormatColorFillIcon from "@mui/icons-material/FormatColorFill";
import SwapHorizIcon from "@mui/icons-material/SwapHoriz";
import FormatColorResetIcon from "@mui/icons-material/FormatColorReset";

// 预设颜色
const PRESET_COLORS = [
  "#FFFFFF",
  "#F5F5F5",
  "#E0E0E0",
  "#BDBDBD",
  "#9E9E9E",
  "#757575",
  "#424242",
  "#212121",
  "#FFEBEE",
  "#FFCDD2",
  "#EF9A9A",
  "#E57373",
  "#EF5350",
  "#F44336",
  "#E53935",
  "#D32F2F",
  "#F3E5F5",
  "#E1BEE7",
  "#CE93D8",
  "#BA68C8",
  "#AB47BC",
  "#9C27B0",
  "#8E24AA",
  "#7B1FA2",
  "#E8EAF6",
  "#C5CAE9",
  "#9FA8DA",
  "#7986CB",
  "#5C6BC0",
  "#3F51B5",
  "#3949AB",
  "#303F9F",
  "#E3F2FD",
  "#BBDEFB",
  "#90CAF9",
  "#64B5F6",
  "#42A5F5",
  "#2196F3",
  "#1E88E5",
  "#1976D2",
  "#E0F2F1",
  "#B2DFDB",
  "#80CBC4",
  "#4DB6AC",
  "#26A69A",
  "#009688",
  "#00897B",
  "#00695C",
  "#E8F5E8",
  "#C8E6C9",
  "#A5D6A7",
  "#81C784",
  "#66BB6A",
  "#4CAF50",
  "#43A047",
  "#388E3C",
  "#FFF3E0",
  "#FFE0B2",
  "#FFCC80",
  "#FFB74D",
  "#FFA726",
  "#FF9800",
  "#FB8C00",
  "#F57C00",
];

// 最近使用颜色的本地存储key
const RECENT_COLORS_KEY = "unifiedColorPicker_recentColors";
const MAX_RECENT_COLORS = 16;

export type ColorPickerMode = "simple" | "advanced" | "gradient";

export interface ColorPickerProps {
  color: string | string[];
  onChange: (color: string | string[]) => void;
  mode?: ColorPickerMode;
  disabled?: boolean;
  size?: "small" | "medium" | "large";
  showPresets?: boolean;
  showRecents?: boolean;
  allowTransparent?: boolean;
  pickerType?: "sketch" | "chrome";
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => (
  <div hidden={value !== index} style={{ paddingTop: 16 }}>
    {value === index && children}
  </div>
);

export const UnifiedColorPicker: React.FC<ColorPickerProps> = ({
  color,
  onChange,
  mode = "simple",
  disabled = false,
  size = "medium",
  showPresets = true,
  showRecents = true,
  allowTransparent = false,
  pickerType = "sketch",
}) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [recentColors, setRecentColors] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState(0);

  // Gradient specific states
  const [gradientAngle, setGradientAngle] = useState(0);
  const [gradientColors, setGradientColors] = useState<string[]>(
    Array.isArray(color) ? color : ["#000000", "#ffffff"]
  );

  const open = Boolean(anchorEl);

  // 获取颜色尺寸
  const getColorSize = () => {
    switch (size) {
      case "small":
        return { width: 20, height: 20 };
      case "large":
        return { width: 40, height: 40 };
      default:
        return { width: 30, height: 30 };
    }
  };

  // 加载最近使用的颜色
  useEffect(() => {
    const stored = localStorage.getItem(RECENT_COLORS_KEY);
    if (stored) {
      try {
        setRecentColors(JSON.parse(stored));
      } catch (e) {
        console.warn("Failed to parse recent colors from localStorage");
      }
    }
  }, []);

  // 保存颜色到最近使用
  const saveToRecents = useCallback((newColor: string) => {
    if (!newColor || newColor === "transparent") return;

    setRecentColors((prev) => {
      const filtered = prev.filter((c) => c !== newColor);
      const updated = [newColor, ...filtered].slice(0, MAX_RECENT_COLORS);
      localStorage.setItem(RECENT_COLORS_KEY, JSON.stringify(updated));
      return updated;
    });
  }, []);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (disabled) return;
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleColorChange = useCallback(
    (colorResult: ColorResult | string) => {
      let newColor: string;
      if (typeof colorResult === "string") {
        newColor = colorResult;
      } else {
        newColor = colorResult.hex;
      }

      if (mode === "gradient") {
        // 梯度模式下更新当前激活的颜色
        const newGradientColors = [...gradientColors];
        newGradientColors[activeTab] = newColor;
        setGradientColors(newGradientColors);
        onChange(newGradientColors);
      } else {
        onChange(newColor);
        saveToRecents(newColor);
      }
    },
    [mode, gradientColors, activeTab, onChange, saveToRecents]
  );

  const handleGradientAngleChange = (
    event: Event,
    newValue: number | number[]
  ) => {
    setGradientAngle(newValue as number);
  };

  const handleSwapGradientColors = () => {
    const swapped = [gradientColors[1], gradientColors[0]];
    setGradientColors(swapped);
    onChange(swapped);
  };

  const handlePresetColorClick = (presetColor: string) => {
    handleColorChange(presetColor);
    if (mode !== "gradient") {
      handleClose();
    }
  };

  const handleTransparentClick = () => {
    onChange("transparent");
    handleClose();
  };

  // 渲染颜色触发器
  const renderTrigger = () => {
    const { width, height } = getColorSize();
    const currentColor = Array.isArray(color) ? color[0] : color;

    if (mode === "gradient" && Array.isArray(color)) {
      return (
        <Box
          onClick={handleClick}
          sx={{
            width,
            height,
            borderRadius: "50%",
            cursor: disabled ? "default" : "pointer",
            transition: "all 0.3s ease",
            background: `linear-gradient(${gradientAngle}deg, ${color[0]}, ${color[1]})`,
            border: "2px solid #e0e0e0",
            boxShadow: "0 0 5px rgba(0,0,0,0.1)",
            "&:hover": disabled
              ? {}
              : {
                  boxShadow: "0 0 10px rgba(0,0,0,0.2)",
                  transform: "scale(1.05)",
                },
          }}
        />
      );
    }

    return (
      <Box
        onClick={handleClick}
        sx={{
          width,
          height,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: disabled ? "default" : "pointer",
          transition: "all 0.2s ease",
          borderRadius: 1,
          backgroundColor:
            currentColor === "transparent" ? "transparent" : currentColor,
          border: "1px solid rgba(0,0,0,0.08)",
          backgroundImage:
            currentColor === "transparent"
              ? "linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%)"
              : undefined,
          backgroundSize:
            currentColor === "transparent" ? "8px 8px" : undefined,
          backgroundPosition:
            currentColor === "transparent"
              ? "0 0, 0 4px, 4px -4px, -4px 0px"
              : undefined,
          "&:hover": disabled
            ? {}
            : {
                boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                transform: "scale(1.03)",
              },
        }}
      >
        {size !== "small" && (
          <FormatColorFillIcon
            style={{
              color:
                currentColor === "transparent"
                  ? "#666"
                  : currentColor === "#FFFFFF"
                  ? "#333"
                  : currentColor,
              fontSize: size === "large" ? 24 : 16,
            }}
          />
        )}
      </Box>
    );
  };

  // 渲染颜色网格
  const renderColorGrid = (colors: string[]) => (
    <Grid container spacing={1}>
      {colors.map((presetColor, index) => (
        <Grid item key={index}>
          <Box
            onClick={() => handlePresetColorClick(presetColor)}
            sx={{
              width: 24,
              height: 24,
              backgroundColor: presetColor,
              border: "1px solid #e0e0e0",
              borderRadius: 0.5,
              cursor: "pointer",
              transition: "transform 0.2s",
              "&:hover": {
                transform: "scale(1.1)",
                boxShadow: "0 2px 4px rgba(0,0,0,0.2)",
              },
            }}
          />
        </Grid>
      ))}
    </Grid>
  );

  // 渲染Popover内容
  const renderPopoverContent = () => {
    const currentColor = Array.isArray(color) ? color[0] : color;

    if (mode === "simple") {
      return (
        <Box sx={{ p: 2, width: 250 }}>
          {/* 颜色选择器 */}
          {pickerType === "sketch" ? (
            <SketchPicker
              color={currentColor}
              onChangeComplete={handleColorChange}
              disableAlpha={false}
            />
          ) : (
            <ChromePicker color={currentColor} onChange={handleColorChange} />
          )}

          {/* 透明选项 */}
          {allowTransparent && (
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                fullWidth
                onClick={handleTransparentClick}
                startIcon={<FormatColorResetIcon />}
              >
                Transparent
              </Button>
            </Box>
          )}
        </Box>
      );
    }

    if (mode === "gradient") {
      return (
        <Box sx={{ p: 2, width: 300 }}>
          <Typography variant="subtitle1" gutterBottom>
            Gradient Editor
          </Typography>

          {/* 颜色选择标签 */}
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            sx={{ mb: 2 }}
          >
            <Tab label="Color 1" />
            <Tab label="Color 2" />
          </Tabs>

          {/* 颜色控制 */}
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Box
              sx={{
                width: 50,
                height: 25,
                backgroundColor: gradientColors[0],
                border: "2px solid #e0e0e0",
                borderRadius: 1,
                cursor: "pointer",
              }}
              onClick={() => setActiveTab(0)}
            />
            <IconButton onClick={handleSwapGradientColors} size="small">
              <SwapHorizIcon />
            </IconButton>
            <Box
              sx={{
                width: 50,
                height: 25,
                backgroundColor: gradientColors[1],
                border: "2px solid #e0e0e0",
                borderRadius: 1,
                cursor: "pointer",
              }}
              onClick={() => setActiveTab(1)}
            />
          </Box>

          {/* 颜色选择器 */}
          <ChromePicker
            color={gradientColors[activeTab]}
            onChange={handleColorChange}
          />

          {/* 角度控制 */}
          <Typography variant="subtitle2" gutterBottom sx={{ mt: 2 }}>
            Angle: {gradientAngle}°
          </Typography>
          <Slider
            value={gradientAngle}
            onChange={handleGradientAngleChange}
            min={0}
            max={360}
            step={1}
            valueLabelDisplay="auto"
            sx={{ mb: 2 }}
          />

          {/* 预览 */}
          <Box
            sx={{
              width: "100%",
              height: 60,
              background: `linear-gradient(${gradientAngle}deg, ${gradientColors[0]}, ${gradientColors[1]})`,
              borderRadius: 1,
              boxShadow: "inset 0 0 5px rgba(0,0,0,0.1)",
            }}
          />
        </Box>
      );
    }

    // Advanced mode
    return (
      <Box sx={{ p: 2, width: 300 }}>
        <Tabs
          value={activeTab}
          onChange={(_, newValue) => setActiveTab(newValue)}
        >
          <Tab label="Picker" />
          {showPresets && <Tab label="Presets" />}
          {showRecents && <Tab label="Recent" />}
        </Tabs>

        <TabPanel value={activeTab} index={0}>
          {pickerType === "sketch" ? (
            <SketchPicker
              color={currentColor}
              onChangeComplete={handleColorChange}
              disableAlpha={false}
            />
          ) : (
            <ChromePicker color={currentColor} onChange={handleColorChange} />
          )}

          {allowTransparent && (
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                fullWidth
                onClick={handleTransparentClick}
                startIcon={<FormatColorResetIcon />}
              >
                Transparent
              </Button>
            </Box>
          )}
        </TabPanel>

        {showPresets && (
          <TabPanel value={activeTab} index={1}>
            <Typography variant="body2" gutterBottom>
              Preset Colors
            </Typography>
            {renderColorGrid(PRESET_COLORS)}
          </TabPanel>
        )}

        {showRecents && (
          <TabPanel value={activeTab} index={showPresets ? 2 : 1}>
            <Typography variant="body2" gutterBottom>
              Recent Colors
            </Typography>
            {recentColors.length > 0 ? (
              renderColorGrid(recentColors)
            ) : (
              <Typography variant="body2" color="textSecondary">
                No recent colors
              </Typography>
            )}
          </TabPanel>
        )}
      </Box>
    );
  };

  return (
    <>
      <Tooltip title={disabled ? "" : "Select Color"} arrow>
        <span>{renderTrigger()}</span>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        PaperProps={{
          elevation: 12,
          sx: {
            borderRadius: 2,
            border: "1px solid rgba(0,0,0,0.08)",
            overflow: "hidden",
          },
        }}
      >
        {renderPopoverContent()}
      </Popover>
    </>
  );
};

export default UnifiedColorPicker;

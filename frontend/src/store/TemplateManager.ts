import { makeAutoObservable, runInAction } from "mobx";
import {
  Template,
  TemplateFilter,
  CreateTemplateRequest,
  TemplateCategory,
  TemplateCanvasState,
  TemplateMetadata,
  TrackType,
} from "../types";
import * as templateService from "../services/templateService";
import { Store } from "./Store";

export class TemplateManager {
  // 模板列表
  templates: Template[] = [];

  // 当前选中的模板
  selectedTemplate: Template | null = null;

  // 加载状态
  isLoading = false;
  isCreating = false;
  isApplying = false;

  // 分页信息
  currentPage = 1;
  totalPages = 1;
  totalTemplates = 0;
  limit = 20;

  // 过滤和搜索
  currentFilter: TemplateFilter = {};
  searchQuery = "";

  // 错误信息
  error: string | null = null;

  // 热门和最新模板缓存
  popularTemplates: Template[] = [];
  latestTemplates: Template[] = [];

  constructor(private store: Store) {
    makeAutoObservable(this);
  }

  /**
   * 加载模板列表
   */
  async loadTemplates(filter: TemplateFilter = {}, page: number = 1) {
    this.isLoading = true;
    this.error = null;

    try {
      const response = await templateService.getTemplates(
        filter,
        page,
        this.limit
      );

      runInAction(() => {
        this.templates = response.templates;
        this.currentPage = response.page;
        this.totalPages = response.totalPages;
        this.totalTemplates = response.total;
        this.currentFilter = filter;
      });
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : "加载模板失败";
      });
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  /**
   * 搜索模板
   */
  async searchTemplates(
    query: string,
    filter: TemplateFilter = {},
    page: number = 1
  ) {
    this.isLoading = true;
    this.error = null;
    this.searchQuery = query;

    try {
      const response = await templateService.searchTemplates(
        query,
        filter,
        page,
        this.limit
      );

      runInAction(() => {
        this.templates = response.templates;
        this.currentPage = response.page;
        this.totalPages = response.totalPages;
        this.totalTemplates = response.total;
        this.currentFilter = filter;
      });
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : "搜索模板失败";
      });
    } finally {
      runInAction(() => {
        this.isLoading = false;
      });
    }
  }

  /**
   * 加载热门模板
   */
  async loadPopularTemplates() {
    try {
      const templates = await templateService.getPopularTemplates(10);
      runInAction(() => {
        this.popularTemplates = templates;
      });
    } catch (error) {
      console.error("加载热门模板失败:", error);
    }
  }

  /**
   * 加载最新模板
   */
  async loadLatestTemplates() {
    try {
      const templates = await templateService.getLatestTemplates(10);
      runInAction(() => {
        this.latestTemplates = templates;
      });
    } catch (error) {
      console.error("加载最新模板失败:", error);
    }
  }

  /**
   * 选择模板
   */
  selectTemplate(template: Template | null) {
    this.selectedTemplate = template;
  }

  /**
   * 应用模板到当前项目
   */
  async applyTemplate(template: Template) {
    this.isApplying = true;
    this.error = null;

    try {
      // 增加使用次数
      await templateService.incrementTemplateUsage(template.id);

      // 应用模板状态到当前项目
      await this.applyTemplateToProject(template.canvasState);

      runInAction(() => {
        this.selectedTemplate = null;
      });

      return true;
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : "应用模板失败";
      });
      return false;
    } finally {
      runInAction(() => {
        this.isApplying = false;
      });
    }
  }

  /**
   * 将模板状态应用到项目
   */
  private async applyTemplateToProject(templateState: TemplateCanvasState) {
    // 设置画布尺寸和背景
    this.store.setCanvasSize(templateState.width, templateState.height);
    this.store.setBackgroundColor(templateState.backgroundColor);

    // 清空当前项目
    this.store.setEditorElements([]);
    this.store.animations = [];
    this.store.captions = [];

    // 应用模板元素
    if (templateState.elements.length > 0) {
      // 为模板元素分配trackId
      const elementsWithTrackIds = this.assignTrackIdsToElements(
        templateState.elements
      );

      // 使用导入功能来应用模板状态
      const canvasStateJson = JSON.stringify({
        width: templateState.width,
        height: templateState.height,
        backgroundColor: templateState.backgroundColor,
        elements: elementsWithTrackIds,
        animations: templateState.animations,
        captions: templateState.captions,
      });

      await this.store.importCanvasState(canvasStateJson);
    }

    // 更新时间轴
    this.store.updateMaxTime();
    this.store.fitTimelineToContent();
  }

  /**
   * 为模板元素分配trackId，避免时间重叠
   */
  private assignTrackIdsToElements(elements: any[]): any[] {
    const trackManager = this.store.trackManager;

    // 按类型分组元素
    const elementsByType = this.groupElementsByType(elements);
    const result: any[] = [];

    // 为每种类型的元素分配轨道
    for (const [elementType, typeElements] of Object.entries(elementsByType)) {
      const trackType = this.getTrackTypeForElement(elementType);
      const elementsWithTracks = this.assignTracksForType(
        typeElements as any[],
        trackType,
        trackManager
      );
      result.push(...elementsWithTracks);
    }

    return result;
  }

  /**
   * 按元素类型分组
   */
  private groupElementsByType(elements: any[]): Record<string, any[]> {
    return elements.reduce((groups, element) => {
      // 如果元素已经有trackId，保持不变
      if (element.trackId) {
        if (!groups["existing"]) {
          groups["existing"] = [];
        }
        groups["existing"].push(element);
        return groups;
      }

      const type = element.type;
      if (!groups[type]) {
        groups[type] = [];
      }
      groups[type].push(element);
      return groups;
    }, {} as Record<string, any[]>);
  }

  /**
   * 获取元素类型对应的轨道类型
   */
  private getTrackTypeForElement(elementType: string): TrackType {
    if (elementType === "image" || elementType === "video") {
      return "media";
    } else if (elementType === "audio") {
      return "audio";
    } else if (elementType === "text") {
      return "text";
    } else {
      // 对于其他类型（如shape），默认使用text轨道
      return "text";
    }
  }

  /**
   * 为特定类型的元素分配轨道，避免时间重叠
   */
  private assignTracksForType(
    elements: any[],
    trackType: TrackType,
    trackManager: any
  ): any[] {
    // 处理已有trackId的元素
    if (elements.length > 0 && elements[0].trackId) {
      return elements;
    }

    // 按开始时间排序元素
    const sortedElements = [...elements].sort((a, b) => {
      const startA = a.timeFrame?.start || 0;
      const startB = b.timeFrame?.start || 0;
      return startA - startB;
    });

    // 轨道分配算法
    const tracks: { trackId: string; elements: any[] }[] = [];
    const result: any[] = [];

    for (const element of sortedElements) {
      const elementStart = element.timeFrame?.start || 0;
      const elementEnd = element.timeFrame?.end || elementStart + 1000;

      // 查找可用的轨道（没有时间重叠）
      let assignedTrack = tracks.find((track) =>
        this.canElementFitInTrack(element, track.elements)
      );

      if (!assignedTrack) {
        // 创建新轨道
        const newTrack = trackManager.createTrack(trackType);
        assignedTrack = {
          trackId: newTrack.id,
          elements: [],
        };
        tracks.push(assignedTrack);
      }

      // 分配元素到轨道
      assignedTrack.elements.push(element);
      result.push({
        ...element,
        trackId: assignedTrack.trackId,
      });
    }

    return result;
  }

  /**
   * 检查元素是否可以放入指定轨道（无时间重叠）
   */
  private canElementFitInTrack(element: any, trackElements: any[]): boolean {
    const elementStart = element.timeFrame?.start || 0;
    const elementEnd = element.timeFrame?.end || elementStart + 1000;

    return !trackElements.some((existingElement) => {
      const existingStart = existingElement.timeFrame?.start || 0;
      const existingEnd =
        existingElement.timeFrame?.end || existingStart + 1000;

      // 检查时间重叠：如果新元素的开始时间在现有元素的时间范围内，
      // 或者新元素的结束时间在现有元素的时间范围内，
      // 或者新元素完全包含现有元素，则存在重叠
      return (
        (elementStart >= existingStart && elementStart < existingEnd) ||
        (elementEnd > existingStart && elementEnd <= existingEnd) ||
        (elementStart <= existingStart && elementEnd >= existingEnd)
      );
    });
  }

  /**
   * 创建新模板
   */
  async createTemplate(templateData: CreateTemplateRequest) {
    this.isCreating = true;
    this.error = null;

    try {
      const template = await templateService.createTemplate(templateData);

      runInAction(() => {
        // 将新模板添加到列表开头
        this.templates.unshift(template);
        this.totalTemplates += 1;
      });

      return template;
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : "创建模板失败";
      });
      throw error;
    } finally {
      runInAction(() => {
        this.isCreating = false;
      });
    }
  }

  /**
   * 从当前项目创建模板
   */
  async createTemplateFromCurrentProject(
    name: string,
    description: string,
    category: TemplateCategory,
    tags: string[] = [],
    isPublic: boolean = true
  ) {
    // 生成模板画布状态
    const canvasState: TemplateCanvasState = {
      width: this.store.canvasWidth,
      height: this.store.canvasHeight,
      backgroundColor: this.store.backgroundColor,
      elements: this.store.editorElements.map(
        ({ fabricObject, ...element }) => element
      ),
      animations: this.store.animations,
      captions: this.store.captions,
      duration: this.store.maxDuration || 5000,
    };

    const templateData: CreateTemplateRequest = {
      name,
      description,
      category,
      tags,
      isPublic,
      canvasState,
    };

    return await this.createTemplate(templateData);
  }

  /**
   * 生成模板元数据
   */
  generateTemplateMetadata(canvasState: TemplateCanvasState): TemplateMetadata {
    const elements = canvasState.elements;

    return {
      duration: canvasState.duration,
      elementCount: elements.length,
      hasVideo: elements.some((el) => el.type === "video"),
      hasAudio: elements.some((el) => el.type === "audio"),
      hasText: elements.some((el) => el.type === "text"),
      hasImages: elements.some((el) => el.type === "image"),
      hasAnimations: canvasState.animations.length > 0,
      complexity: this.calculateComplexity(elements, canvasState.animations),
      aspectRatio: `${canvasState.width}:${canvasState.height}`,
      resolution: `${canvasState.width}x${canvasState.height}`,
    };
  }

  /**
   * 计算模板复杂度
   */
  private calculateComplexity(
    elements: any[],
    animations: any[]
  ): "simple" | "medium" | "complex" {
    const elementCount = elements.length;
    const animationCount = animations.length;
    const hasVideo = elements.some((el) => el.type === "video");

    if (elementCount <= 3 && animationCount <= 2 && !hasVideo) {
      return "simple";
    } else if (elementCount <= 8 && animationCount <= 5) {
      return "medium";
    } else {
      return "complex";
    }
  }

  /**
   * 删除模板
   */
  async deleteTemplate(templateId: string) {
    try {
      await templateService.deleteTemplate(templateId);

      runInAction(() => {
        this.templates = this.templates.filter((t) => t.id !== templateId);
        this.totalTemplates -= 1;

        if (this.selectedTemplate?.id === templateId) {
          this.selectedTemplate = null;
        }
      });

      return true;
    } catch (error) {
      runInAction(() => {
        this.error = error instanceof Error ? error.message : "删除模板失败";
      });
      return false;
    }
  }

  /**
   * 为模板评分
   */
  async rateTemplate(templateId: string, rating: number) {
    try {
      await templateService.rateTemplate(templateId, rating);

      // 更新本地模板评分
      runInAction(() => {
        const template = this.templates.find((t) => t.id === templateId);
        if (template) {
          // 简单的评分更新逻辑，实际应该从服务器获取最新数据
          const newRatingCount = template.ratingCount + 1;
          const newRating =
            (template.rating * template.ratingCount + rating) / newRatingCount;
          template.rating = newRating;
          template.ratingCount = newRatingCount;
        }
      });

      return true;
    } catch (error) {
      console.error("评分失败:", error);
      return false;
    }
  }

  /**
   * 清空错误信息
   */
  clearError() {
    this.error = null;
  }

  /**
   * 重置状态
   */
  reset() {
    this.templates = [];
    this.selectedTemplate = null;
    this.isLoading = false;
    this.isCreating = false;
    this.isApplying = false;
    this.currentPage = 1;
    this.totalPages = 1;
    this.totalTemplates = 0;
    this.currentFilter = {};
    this.searchQuery = "";
    this.error = null;
    this.popularTemplates = [];
    this.latestTemplates = [];
  }
}

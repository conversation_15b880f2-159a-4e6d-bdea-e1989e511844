import { fabric } from "fabric";

export type Caption = {
  id: string;
  startTime: string;
  endTime: string;
  text: string;
  isSelected?: boolean;
};

// 全局字幕样式配置
export type CaptionStyle = {
  fontSize: number;
  fontFamily: string;
  fontColor: string;
  fontWeight: number;
  textAlign: "left" | "center" | "right";
  lineHeight: number;
  charSpacing: number;
  styles: string[]; // bold, italic, underline等
  strokeWidth: number;
  strokeColor: string;
  shadowColor: string;
  shadowBlur: number;
  shadowOffsetX: number;
  shadowOffsetY: number;
  backgroundColor: string;
  useGradient: boolean;
  gradientColors: string[];
  // 字幕位置信息
  positionX?: number; // X坐标（相对于画布中心的偏移）
  positionY?: number; // Y坐标（相对于默认底部位置的偏移）
  originX?: "left" | "center" | "right"; // 水平对齐原点
  originY?: "top" | "center" | "bottom"; // 垂直对齐原点
};

export type EditorElementBase<T extends string, P> = {
  readonly id: string;
  fabricObject?: fabric.Object;
  name: string;
  readonly type: T;
  placement?: Placement;
  timeFrame: TimeFrame;
  properties: P;
  locked: boolean;
  opacity?: number | 1;
  trackId?: string;
};

export type VideoEditorElement = EditorElementBase<
  "video",
  {
    src: string;
    elementId: string;
    imageObject?: fabric.Image;
    effect: Effect;
    filters: any;
    border?: BorderStyle;
    hasAudio?: boolean;
    originalDuration?: number; // 视频的原始时长（毫秒）
  }
>;
export type ImageEditorElement = EditorElementBase<
  "image",
  {
    src: string;

    elementId: string;
    imageObject?: fabric.Object;
    effect: Effect;
    filters: any;
    border?: BorderStyle;
  }
>;

export type AudioEditorElement = EditorElementBase<
  "audio",
  {
    src: string;
    elementId: string;
    originalDuration?: number; // 音频的原始时长（毫秒）
  }
>;
export type TextEditorElement = EditorElementBase<
  "text",
  {
    text: string;
    fontSize: number;
    fontWeight: number;
    fontFamily: string;
    splittedTexts: fabric.Text[];
    textAlign?: "left" | "center" | "right";
    lineHeight?: number | 10;
    charSpacing?: number | 10;
    styles?: string[];
    fontColor?: string;
    strokeWidth?: number;
    strokeColor?: string;
    shadowColor?: string;
    shadowBlur?: number;
    shadowOffsetX?: number;
    shadowOffsetY?: number;
    useGradient?: boolean;
    gradientColors?: string[];
    backgroundColor?: string;
  }
>;

export type ShapeType =
  | "rect"
  | "circle"
  | "triangle"
  | "polygon"
  | "ellipse"
  | "line"
  | "pentagon"
  | "hexagon"
  | "octagon"
  | "roundedRect"
  | "arch"
  | "parallelogram";

export type ShapeEditorElement = EditorElementBase<
  "shape",
  {
    shapeType: ShapeType;
    fill: string;
    stroke: string;
    strokeWidth: number;
    borderRadius?: number;
    points?: { x: number; y: number }[];
    border?: BorderStyle;
    backgroundColor?: string;
  }
>;

export type EditorElement =
  | VideoEditorElement
  | ImageEditorElement
  | AudioEditorElement
  | TextEditorElement
  | ShapeEditorElement;

export type Placement = {
  x: number;
  y: number;
  cropX?: number;
  cropY?: number;
  cropWidth?: number;
  cropHeight?: number;
  width: number;
  height: number;
  rotation: number;
  scaleX: number;
  scaleY: number;
  flipX: boolean | false;
  flipY: boolean | false;
};

export type TimeFrame = {
  start: number;
  end: number;
};

export type EffectBase<T extends string> = {
  type: T;
};

export type BlackAndWhiteEffect =
  | EffectBase<"none">
  | EffectBase<"blackAndWhite">
  | EffectBase<"sepia">
  | EffectBase<"invert">
  | EffectBase<"saturate">
  | EffectBase<"retro">;
export type Effect = BlackAndWhiteEffect;
export type EffecType = Effect["type"];

export type AnimationBase<T, P = {}> = {
  id: string;
  targetId: string;
  duration: number;
  type: T;
  group: string;
  properties: P;
};

export type FadeInAnimation = AnimationBase<"fadeIn">;
export type FadeOutAnimation = AnimationBase<"fadeOut">;

export type BreatheAnimation = AnimationBase<"breathe">;

export type SlideDirection = "left" | "right" | "top" | "bottom";
export type SlideTextType = "none" | "character";
export type SlideInAnimation = AnimationBase<
  "slideIn",
  {
    direction: SlideDirection;
    useClipPath: boolean;
    textType: "none" | "character";
  }
>;

export type SlideOutAnimation = AnimationBase<
  "slideOut",
  {
    direction: SlideDirection;
    useClipPath: boolean;
    textType: SlideTextType;
  }
>;

export type RotateAnimation = AnimationBase<
  "rotate",
  { rotationDegrees?: number; loops?: number }
>;
export type BounceAnimation = AnimationBase<"bounce", { height?: number }>;
export type ShakeAnimation = AnimationBase<"shake", { intensity?: number }>;
export type FlashAnimation = AnimationBase<"flash">;
export type ZoomAnimation = AnimationBase<"zoom", { scale?: number }>;
export type ZoomInAnimation = AnimationBase<"zoomIn", { scale?: number }>;
export type ZoomOutAnimation = AnimationBase<"zoomOut", { scale?: number }>;

export type Animation =
  | FadeInAnimation
  | FadeOutAnimation
  | SlideInAnimation
  | SlideOutAnimation
  | BreatheAnimation
  | RotateAnimation
  | BounceAnimation
  | ShakeAnimation
  | FlashAnimation
  | ZoomAnimation
  | ZoomInAnimation
  | ZoomOutAnimation;

export type MenuOption =
  | "Design"
  | "Video"
  | "Audio"
  | "Text"
  | "Image"
  | "Export"
  | "Animation"
  | "Effect"
  | "Fill"
  | "uploads"
  | "layers"
  | "Caption"
  | "Shape"
  | "Templates";

export interface BorderStyle {
  width: number;
  color: string;
  style: "solid" | "dashed" | "dotted";
  borderRadius: number;
}

export type TrackType = "media" | "audio" | "text" | "caption";

export interface Track {
  id: string;
  name: string;
  type: TrackType;
  elementIds: string[];
  isVisible?: boolean;
  isLocked?: boolean;
}

// 模板相关类型定义
export interface Template {
  id: string;
  name: string;
  description: string;
  category: TemplateCategory;
  tags: string[];
  thumbnail: string;
  previewVideo?: string;
  canvasState: TemplateCanvasState;
  metadata: TemplateMetadata;
  createdAt: string;
  updatedAt: string;
  isPublic: boolean;
  authorId?: string;
  authorName?: string;
  usageCount: number;
  rating: number;
  ratingCount: number;
}

export interface TemplateCanvasState {
  width: number;
  height: number;
  backgroundColor: string;
  elements: EditorElement[];
  animations: Animation[];
  captions: Caption[];
  duration: number;
}

export interface TemplateMetadata {
  duration: number;
  elementCount: number;
  hasVideo: boolean;
  hasAudio: boolean;
  hasText: boolean;
  hasImages: boolean;
  hasAnimations: boolean;
  complexity: "simple" | "medium" | "complex";
  aspectRatio: string;
  resolution: string;
}

export type TemplateCategory =
  | "business"
  | "social"
  | "education"
  | "entertainment"
  | "marketing"
  | "presentation"
  | "intro"
  | "outro"
  | "logo"
  | "slideshow"
  | "promotional"
  | "tutorial"
  | "news"
  | "sports"
  | "travel"
  | "food"
  | "fashion"
  | "technology"
  | "music"
  | "gaming"
  | "other";

export interface TemplateFilter {
  category?: TemplateCategory;
  tags?: string[];
  hasVideo?: boolean;
  hasAudio?: boolean;
  hasText?: boolean;
  hasAnimations?: boolean;
  duration?: {
    min?: number;
    max?: number;
  };
  complexity?: TemplateMetadata["complexity"];
  aspectRatio?: string;
  sortBy?: "popular" | "newest" | "rating" | "name";
  sortOrder?: "asc" | "desc";
}

export interface CreateTemplateRequest {
  name: string;
  description: string;
  category: TemplateCategory;
  tags: string[];
  isPublic: boolean;
  canvasState: TemplateCanvasState;
}

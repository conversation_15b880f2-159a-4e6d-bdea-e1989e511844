import React from "react";
import { CssBaseline } from "@mui/material";
import { StoreProvider as ElementProvideo } from "./store";
import { StoreProvider } from "./store/store-context";
import Editor from "./editor/Editor";
import { CustomThemeProvider } from "./theme/ThemeContext";
import { LanguageProvider } from "./i18n/LanguageContext";
import LoadingOverlay from "./components/LoadingOverlay";

const App: React.FC = () => {
  return (
    <LanguageProvider>
      <CustomThemeProvider>
        <CssBaseline />
        <StoreProvider>
          <ElementProvideo>
            <LoadingOverlay />
            <Editor />
          </ElementProvideo>
        </StoreProvider>
      </CustomThemeProvider>
    </LanguageProvider>
  );
};

export default App;
